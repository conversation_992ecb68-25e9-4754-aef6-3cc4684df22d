import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  StyleProp,
  ViewStyle,
} from 'react-native';
import Typography from './Typography';
import KioskConfig from '@/config/KioskConfig';

const ToggleTab = ({
  containerStyle,
  tabs,
  getSelectedTab,
}: {
  containerStyle: StyleProp<ViewStyle>;
  tabs: string[];
  getSelectedTab: (tab: string) => void;
}) => {
  const [selectedTab, setSelectedTab] = useState(tabs[0]);
  const slideAnim = useRef(new Animated.Value(0)).current;

  const toggleHandedness = (tab: string) => {
    setSelectedTab(tab);
    getSelectedTab(tab);
  };

  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: selectedTab === tabs[0] ? 0 : 1,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [selectedTab]);

  // Calculate the left position for the animated background
  const backgroundPosition = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '50%'],
  });

  return (
    <View style={[styles.container, containerStyle]}>
      <TouchableOpacity
        style={styles.toggleContainer}
        activeOpacity={0.8}
        onPress={() => toggleHandedness(tabs[selectedTab === tabs[0] ? 1 : 0])}>
        <View style={styles.labelsContainer}>
          {tabs.map(data => (
            <Typography
              variant="toggleTab"
              align="center"
              style={{
                paddingLeft: 10,
              }}
              key={data}
              color={
                selectedTab === data
                  ? KioskConfig.theme.colors.white
                  : KioskConfig.theme.colors.text.inputText
              }>
              {data}
            </Typography>
          ))}
        </View>

        {/* Animated colored background */}
        <Animated.View
          style={[
            styles.slider,
            {
              left: backgroundPosition,
            },
          ]}
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  toggleContainer: {
    position: 'relative',
    width: 300,
    height: 50,
    backgroundColor: KioskConfig.theme.colors.lightGray,
    borderRadius: 25,
    overflow: 'hidden',
    marginBottom: 16,
  },
  labelsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    zIndex: 2,
  },

  slider: {
    position: 'absolute',
    top: 0,
    width: '50%',
    height: '100%',
    backgroundColor: KioskConfig.theme.colors.activeTabBg, // Orange color
    borderRadius: 25,
    zIndex: 1,
  },
});

export default ToggleTab;
