import React, {useState} from 'react';
import {View, StyleSheet, Dimensions, TouchableOpacity} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
  withTiming,
} from 'react-native-reanimated';
import GradientBackground from './GradientBackground';
import Typography from './Typography';
import KioskConfig from '@/config/KioskConfig';
import Icon from './Icon';
import LottieView from 'lottie-react-native';
import {moderateScale} from '@/utils/scale';

// Import your Lottie animation files
const animations = {
  tennis: require('../assest/animation/background.json'),
  // Add more animations as needed
};

const {width: screenWidth} = Dimensions.get('window');

const AdvancedCarouselExample: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const progressValue = useSharedValue<number>(0);
  const lottieRefs = React.useRef<(LottieView | null)[]>([]);

  // Example data for the carousel
  const carouselData = [
    {
      id: '1',
      title: 'Tennis',
      description: 'Experience the thrill of tennis with our premium equipment',
      animation: animations.tennis,
      backgroundColor: 'rgba(0, 100, 255, 0.2)',
    },
    {
      id: '2',
      title: 'Badminton',
      description: 'Lightweight racquets for precision and speed',
      animation: animations.tennis, // Replace with badminton animation
      backgroundColor: 'rgba(255, 100, 0, 0.2)',
    },
    {
      id: '3',
      title: 'Table Tennis',
      description: 'Professional grade paddles for competitive play',
      animation: animations.tennis, // Replace with table tennis animation
      backgroundColor: 'rgba(0, 200, 100, 0.2)',
    },
  ];

  const handleSnapToItem = (index: number) => {
    setActiveIndex(index);

    // Play the Lottie animation for the current slide
    if (lottieRefs.current[index]) {
      lottieRefs.current[index]?.play();
    }

    // Pause other animations
    lottieRefs.current.forEach((ref, i) => {
      if (i !== index && ref) {
        ref.pause();
      }
    });
  };

  // Custom animation for carousel items
  const animationStyle = (value: number) => {
    'worklet';
    const zIndex = interpolate(value, [-1, 0, 1], [10, 20, 10]);
    const opacity = interpolate(
      value,
      [-1, 0, 1],
      [0.8, 1, 0.8],
      Extrapolate.CLAMP,
    );
    const scale = interpolate(
      value,
      [-1, 0, 1],
      [0.9, 1, 0.9],
      Extrapolate.CLAMP,
    );
    const translateY = interpolate(
      value,
      [-1, 0, 1],
      [30, 0, 30],
      Extrapolate.CLAMP,
    );

    return {
      zIndex,
      opacity,
      transform: [{scale}, {translateY}],
    };
  };

  // Custom render function for carousel items
  const renderItem = ({item, index, animationValue}: any) => {
    const animatedStyle = useAnimatedStyle(() => {
      return animationStyle(animationValue.value);
    });

    return (
      <Animated.View
        style={[
          styles.itemContainer,
          {backgroundColor: item.backgroundColor},
          animatedStyle,
        ]}>
        <View style={styles.contentContainer}>
          <Typography variant="faqAnswer" style={styles.title}>
            {item.title}
          </Typography>

          <Typography variant="customButton" style={styles.description}>
            {item.description}
          </Typography>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              // Handle button press
              console.log(`Selected ${item.title}`);

              // Play animation when button is pressed
              if (lottieRefs.current[index]) {
                lottieRefs.current[index]?.play();
              }
            }}>
            <Typography variant="customButton" style={styles.buttonText}>
              Select {item.title}
            </Typography>
          </TouchableOpacity>
        </View>

        <View style={styles.animationContainer}>
          <LottieView
            ref={ref => {
              if (lottieRefs.current.length <= index) {
                lottieRefs.current.push(ref);
              } else {
                lottieRefs.current[index] = ref;
              }
            }}
            source={item.animation}
            style={styles.animation}
            loop={true}
            autoPlay={index === activeIndex}
            speed={0.7}
          />
        </View>
      </Animated.View>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.container}>
        <Typography variant="faqAnswer" style={styles.pageTitle}>
          Advanced Reanimated Carousel
        </Typography>

        <View style={styles.carouselContainer}>
          <Carousel
            loop
            width={screenWidth * 0.8}
            height={500}
            autoPlay={false}
            data={carouselData}
            scrollAnimationDuration={1000}
            onProgressChange={(offsetProgress, absoluteProgress) => {
              progressValue.value = absoluteProgress;
            }}
            onSnapToItem={handleSnapToItem}
            renderItem={renderItem}
            customAnimation={animationStyle}
            mode="parallax"
            modeConfig={{
              parallaxScrollingScale: 0.9,
              parallaxScrollingOffset: 50,
            }}
          />
        </View>

        {/* Custom pagination */}
        <View style={styles.paginationContainer}>
          {carouselData.map((_, index) => {
            const animatedDotStyle = useAnimatedStyle(() => {
              const width = interpolate(
                progressValue.value,
                [index - 1, index, index + 1],
                [10, 30, 10],
                Extrapolate.CLAMP,
              );

              const opacity = interpolate(
                progressValue.value,
                [index - 1, index, index + 1],
                [0.5, 1, 0.5],
                Extrapolate.CLAMP,
              );

              return {
                width,
                opacity,
                backgroundColor:
                  index === activeIndex
                    ? KioskConfig.theme.colors.blue
                    : 'rgba(255, 255, 255, 0.5)',
              };
            });

            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  // Manually set the carousel to this index
                  setActiveIndex(index);
                  progressValue.value = withTiming(index);
                }}>
                <Animated.View
                  style={[styles.paginationDot, animatedDotStyle]}
                />
              </TouchableOpacity>
            );
          })}
        </View>

        <View style={styles.infoContainer}>
          <Typography variant="customButton" style={styles.infoText}>
            This advanced example demonstrates custom animations, Lottie
            integration, and animated pagination indicators.
          </Typography>
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pageTitle: {
    fontSize: moderateScale(32),
    fontWeight: 'bold',
    marginBottom: 30,
    color: KioskConfig.theme.colors.white,
  },
  carouselContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemContainer: {
    flex: 1,
    flexDirection: 'row',
    borderRadius: 20,
    overflow: 'hidden',
    padding: 20,
    margin: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
    padding: 20,
  },
  title: {
    fontSize: moderateScale(32),
    fontWeight: 'bold',
    marginBottom: 15,
    color: KioskConfig.theme.colors.white,
  },
  description: {
    fontSize: moderateScale(18),
    marginBottom: 30,
    color: KioskConfig.theme.colors.white,
  },
  actionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: KioskConfig.theme.colors.white,
    fontWeight: 'bold',
    fontSize: moderateScale(16),
  },
  animationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  animation: {
    width: 300,
    height: 300,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  paginationDot: {
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  infoContainer: {
    marginTop: 30,
    padding: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 10,
    width: '80%',
  },
  infoText: {
    textAlign: 'center',
    color: KioskConfig.theme.colors.white,
  },
});

export default AdvancedCarouselExample;
