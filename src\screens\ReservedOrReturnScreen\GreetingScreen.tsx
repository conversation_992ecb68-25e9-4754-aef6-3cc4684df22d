import React, {useEffect, useRef} from 'react';
import {View, StyleSheet} from 'react-native';
import GradientBackground from '../../components/GradientBackground';
import KioskConfig from '../../config/KioskConfig';
import images from '../../config/images';
import Typography from '../../components/Typography';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '../../navigation';
import {moderateScale, scale, verticalScale} from '@/utils/scale';
import FastImage from 'react-native-fast-image';
import {useAuthStore} from '@/store/authStore';

const GreetingScreen = () => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const isRendered = useRef(true);
  const {user} = useAuthStore();

  useEffect(() => {
    return () => {
      isRendered.current = false;
    };
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (isRendered.current) {
        navigation.navigate('CheckOutOrReturnScreen');
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [navigation]);

  return (
    <GradientBackground
      cart={true}
      onCartPress={() => navigation.navigate('CheckOutOrReturnScreen')}>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <FastImage
            source={images.userProfile}
            style={styles.profileImage}
            resizeMode="cover"
          />
          <Typography
            variant="userTitle"
            color={KioskConfig.theme.colors.white}>
            Hello {user?.name || 'User'}
          </Typography>
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: verticalScale(100),
  },
  contentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileImage: {
    width: scale(400),
    height: verticalScale(400),
    borderRadius: 9999,
    marginBottom: 30,
    overflow: 'hidden',
  },

  greetingText: {
    fontSize: moderateScale(96),
    color: KioskConfig.theme.colors.white,
    textAlign: 'center',
    fontWeight: '300',
    letterSpacing: -1,
  },
});

export default GreetingScreen;
