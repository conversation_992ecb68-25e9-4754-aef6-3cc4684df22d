import React from 'react';
import {View, StyleSheet} from 'react-native';
import Typography from './Typography';
import KioskConfig from '@/config/KioskConfig';
import CustomButton from './CustomButton';
import {moderateScale, scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';

const SelectorFlow = ({
  from: _from,
  title,
  description,
  btnText1,
  btnText2,
  onPress = () => {},
  onPress2 = () => {},
}: {
  from?: string | undefined;
  title?: string;
  description?: string;
  btnText1?: {
    text: string;
    text2?: string;
  };
  btnText2?: {
    text: string;
    text2?: string;
  };
  onPress?: () => void;
  onPress2?: () => void;
}) => {
  const {t} = useLanguageStore();

  const defaultTitle = t('selectorFlow.title');
  const defaultDescription = t('selectorFlow.description');
  const defaultBtnText1 = {
    text: t('selectorFlow.btnText1'),
    text2: '',
  };
  const defaultBtnText2 = {
    text: t('selectorFlow.btnText2'),
    text2: '',
  };

  return (
    <View style={styles.container}>
      <Typography
        variant="faqAnswer"
        color={KioskConfig.theme.colors.text.highlight}
        style={styles.title}>
        {title || defaultTitle}
      </Typography>
      <Typography
        variant="faqAnswer"
        style={styles.subTitle}
        color={KioskConfig.theme.colors.white}>
        {description || defaultDescription}
      </Typography>
      <View style={styles.buttonContainer}>
        <CustomButton
          text={btnText1?.text || defaultBtnText1.text}
          text2={btnText1?.text2 || defaultBtnText1.text2}
          style={styles.button}
          onPress={onPress}
        />
        <CustomButton
          text={btnText2?.text || defaultBtnText2.text}
          text2={btnText2?.text2 || defaultBtnText2.text2}
          style={styles.button}
          onPress={onPress2}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 100,
  },
  title: {
    marginBottom: 10,
    fontSize: moderateScale(24),
  },
  subTitle: {
    marginBottom: verticalScale(60),
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: scale(50),
  },
  button: {
    minWidth: scale(520),
    height: verticalScale(172),
  },
});
export default SelectorFlow;
