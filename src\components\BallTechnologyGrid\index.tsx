import React from 'react';
import {View, StyleSheet} from 'react-native';
import Typography from '../Typography';
import KioskConfig from '@/config/KioskConfig';

const data = [
  {
    title: 'HD PRO CLOTH',
    desc: "Ultra-high specification, ultra-visible cloth technology for the world's elite tournaments and players. ultra-visible cloth technology for the world's elite tournaments and players.",
  },
  {
    title: 'HD CLOTH',
    desc: 'Dunlop HD Cloth stays brighter for longer. So no matter how long the game goes on, your opponent will fade before the ball does.',
  },
  {
    title: 'CLAY GUARD',
    desc: 'Developed for top-level clay court play, the specially treated premium woven cloth means the ball stays brighter for longer.',
  },
  {
    title: 'HI-VIS CLOTH',
    desc: 'This special dye application for our mid-range woven tennis balls makes them more visible. ultra-visible cloth technology for the world elite tournaments and players.',
  },

  {
    title: 'FLUORO CLOTH',
    desc: 'Dunlop Fluoro Cloth is a premium woven cloth that provides excellent durability for all levels of play.',
  },
  {
    title: 'MAX GLO FELT',
    desc: 'Premium woven cloth with high-visibility technology for greater ball durability and longer lasting play.',
  },
  {
    title: 'DURAFELT HD CLOTH',
    desc: 'Dunlop Durafelt HD cloth is a premium non-woven cloth that delivers superb consistency with good durability.',
  },

  {
    title: 'HD PRO CORE',
    desc: 'The ultimate high-performance core from Dunlop using premium materials to create ultra-consistent performance. ',
  },
  {
    title: 'CARBON CORE',
    desc: 'Dunlop Carbon Core technology uses carbon black pigments to deliver enhanced core pressure retention.',
  },
  {
    title: 'HD CORE',
    desc: 'A high-specification, re-mastered and re-engineered version of the classic Dunlop Fort Core.',
  },
  {
    title: 'MAX CORE',
    desc: 'Dunlop Max Core technology is specially engineered for use with lighter, non-woven cloth for all-round playability. ultra-visible cloth technology for the world elite tournaments and players.',
  },
];

const BallTechnologyGrid: React.FC = () => {
  return (
    <View style={styles.container}>
      {data?.map((item, index) => (
        <View key={index} style={styles.card}>
          <Typography variant="heading" style={styles.title}>
            {item.title}
          </Typography>
          <Typography variant="subtitle" style={styles.desc}>
            {item.desc}
          </Typography>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
  },
  card: {
    width: '31%', // Slightly less than 1/3 to account for spacing
    marginBottom: 25,
    padding: 12,
    borderRadius: 8,
  },
  title: {
    color: KioskConfig.theme.colors.white,
    marginBottom: 8,
  },
  desc: {
    color: '#aaa',
  },
});

export default BallTechnologyGrid;
