import React from 'react';
import {View, ScrollView, StyleSheet, Alert} from 'react-native';
import Typography from '../components/Typography';
import KioskConfig from '@/config/KioskConfig';

/**
 * Demo screen to showcase all typography variants
 */
const TypographyDemo = () => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Typography variant="heading" style={styles.typographyItem}>
          Heading - Helvetica Neue Bold (32px)
        </Typography>
        <Typography variant="title" style={styles.typographyItem}>
          Title - Helvetica Neue Bold (28px)
        </Typography>
        <Typography variant="sectionTitle" style={styles.typographyItem}>
          Section Title - Helvetica Neue Medium (22px)
        </Typography>
        <Typography variant="subtitle" style={styles.typographyItem}>
          Subtitle - Helvetica Neue Medium (18px)
        </Typography>
        <Typography variant="subtitle2" style={styles.typographyItem}>
          Subtitle 2 - Helvetica Neue Medium (24px)
        </Typography>
      </View>

      <View style={styles.section}>
        <Typography variant="body" style={styles.typographyItem}>
          Body - Helvetica Neue Regular (14px) - This is the default body text
          style used for most content in the app.
        </Typography>
        <Typography variant="bodyMedium" style={styles.typographyItem}>
          Body Medium - Helvetica Neue Medium (16px) - Similar to body but with
          medium weight and larger size.
        </Typography>
        <Typography variant="bodyMedium1" style={styles.typographyItem}>
          Body Medium 1 - Helvetica Neue Regular (14px) - This variant has a
          smaller line height.
        </Typography>
        <Typography variant="caption" style={styles.typographyItem}>
          Caption - Helvetica Neue Regular (12px) - Used for secondary or
          supporting text.
        </Typography>
      </View>

      <View style={styles.section}>
        <Typography variant="button" style={styles.typographyItem}>
          Button - Helvetica Neue Medium (14px)
        </Typography>
        <Typography variant="badgeText" style={styles.typographyItem}>
          Badge Text - Helvetica Neue Bold (14px)
        </Typography>
        <Typography variant="permissionTitle" style={styles.typographyItem}>
          Permission Title - Helvetica Neue Bold (18px)
        </Typography>
        <Typography variant="parkTitle" style={styles.typographyItem}>
          Park Title - Helvetica Neue Bold (18px)
        </Typography>
      </View>

      <View style={styles.section}>
        <Typography variant="cocogooseRegular" style={styles.typographyItem}>
          Cocogoose Regular - Primary Cocogoose font (14px)
        </Typography>
        <Typography
          variant="cocogooseRegular"
          style={[styles.typographyItem, {fontSize: 18}]}>
          Cocogoose Regular - Custom size (18px)
        </Typography>
        <Typography variant="subTitle3" style={styles.typographyItem}>
          SubTitle 3 - Cocogoose Regular (22px)
        </Typography>
        <Typography variant="subTitle4" style={styles.typographyItem}>
          SubTitle 4 - Helvetica Neue Bold (20px)
        </Typography>
        <Typography variant="communitySubDetail" style={styles.typographyItem}>
          Community Sub Detail - Helvetica Neue Medium (14px)
        </Typography>
        <Typography variant="moreText" style={styles.typographyItem}>
          More Text - Helvetica Neue Medium (12px)
        </Typography>
        <Typography variant="tagTitle" style={styles.typographyItem}>
          Tag Title - Helvetica Neue Medium (10px)
        </Typography>
      </View>

      <View style={styles.section}>
        <Typography
          variant="heading"
          color="#1E88E5"
          align="center"
          style={styles.typographyItem}>
          Custom Color and Alignment
        </Typography>
        <Typography
          variant="body"
          style={[styles.typographyItem, styles.customStyle]}>
          Typography with custom styles
        </Typography>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  section: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    shadowColor: KioskConfig.theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  typographyItem: {
    marginBottom: 16,
  },
  customStyle: {
    backgroundColor: '#E8F5E9',
    padding: 10,
    borderRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  cardWithCocogoose: {
    marginTop: 16,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#FF5722',
  },
  cardTitle: {
    marginBottom: 12,
    color: '#FF5722',
  },
  cardDescription: {
    marginBottom: 16,
  },
  cardButton: {
    backgroundColor: '#FF5722',
    borderRadius: 4,
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
});

export default TypographyDemo;
