import React, {useRef} from 'react';
import {
  View,
  StyleSheet,
  TouchableWithoutFeedback,
  Dimensions,
} from 'react-native';
import Video, {VideoRef} from 'react-native-video';
import images from '../config/images';
import {useLogout} from '@/hooks/queries/useAuth';

interface InactivityScreenProps {
  onContinue: () => void;
}

const {width, height} = Dimensions.get('window');

const InactivityScreen: React.FC<InactivityScreenProps> = ({onContinue}) => {
  const videoRef = useRef<VideoRef>(null);
  const logout = useLogout();

  // Handle user interaction with the screen
  const handlePress = () => {
    // First call the onContinue callback to handle animation and cleanup
    onContinue();

    // Complete the logout process
    logout.mutate();
  };

  return (
    <TouchableWithoutFeedback onPress={handlePress}>
      <View style={styles.container}>
        <Video
          ref={videoRef}
          source={images.goRacketVideo as any} // Type assertion to avoid TypeScript error
          style={styles.video}
          resizeMode="cover"
          repeat
          muted
          playInBackground={false}
          playWhenInactive={false}
          onError={error => console.error('Video playback error:', error)}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  video: {
    width: width,
    height: height,
  },
});

export default InactivityScreen;
