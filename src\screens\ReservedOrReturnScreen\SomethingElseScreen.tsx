import React, {useRef, useEffect} from 'react';
import GradientBackground from '../../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import CustomButton from '../../components/CustomButton';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {useNavigation, RouteProp, useIsFocused} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import LottieView from 'lottie-react-native';
import animation from '@/config/animation';
import {scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';
import {useLogout} from '@/hooks/queries/useAuth';
import {clearTokenStorage} from '@/services/api';
export type SelectBrandScreenProps = {
  route: RouteProp<MainStackParamList, 'SelectBrand'>;
};

const SomethingElseScreen = ({route}: SelectBrandScreenProps) => {
  const {from} = route.params || {};
  console.log('from=====>>>>>', from);
  const {t} = useLanguageStore();
  const isMounted = useRef(true);
  const isFocused = useIsFocused();
  const logout = useLogout();
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();

  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  const buttons = [
    {
      id: 1,
      text:
        from === 'racquet' || from === 'padel'
          ? 'somethingElse.racquet'
          : 'somethingElse.needRacquet',
      onPress: () => {
        if (from === 'racquet' || from === 'padel') {
          navigation.navigate('SelectSport', {from: 'ball'});
        } else {
          navigation.navigate('SelectSport', {from: 'racquet'});
        }
      },
    },
    {
      id: 2,
      text:
        from === 'racquet'
          ? 'somethingElse.anotherRacquet'
          : from === 'padel'
            ? 'somethingElse.padel1'
            : 'somethingElse.morBalls',
      onPress: () => {
        if (from === 'racquet') {
          navigation.navigate('SelectSport', {from: 'racquet'});
        } else if (from === 'padel') {
          navigation.navigate('SelectBrandsPadel', {from: 'padel'});
        } else {
          navigation.navigate('SelectSport', {from: 'ball'});
        }
      },
    },
    {
      id: 3,
      text: 'somethingElse.continueShopping',
      onPress: () => navigation.navigate('Home'),
    },
  ];

  return (
    <GradientBackground>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <Typography
            variant="dispensingText"
            color={KioskConfig.theme.colors.text.highlight}>
            {t('somethingElse.title')}
          </Typography>
          <View style={styles.buttonContainer}>
            {buttons.map(button => (
              <CustomButton
                key={button?.id}
                style={styles.button}
                text={t(button.text)}
                onPress={button.onPress}
              />
            ))}
          </View>
          <LottieView
            autoPlay
            loop={false}
            source={animation.countDown}
            style={styles.animation}
            speed={1}
            onAnimationFinish={() => {
              if (isMounted.current && isFocused) {
                // navigation.navigate('Home');
                // Clear token storage and logout
                clearTokenStorage();
                logout.mutate();
                // navigation.navigate('Home');
                navigation.navigate('ThankYouScreen');
              }
            }}
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    paddingBottom: 200,
  },
  contentContainer: {
    alignItems: 'center',
  },

  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 60,
    marginTop: 30,
  },
  button: {
    minWidth: scale(520),
    height: verticalScale(172),
  },

  animation: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    transform: [{translateY: '10%'}],
    height: '100%',
  },
});
export default SomethingElseScreen;
