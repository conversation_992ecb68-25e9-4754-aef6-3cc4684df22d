import CustomButton from '@/components/CustomButton';
import GradientBackground from '@/components/GradientBackground';
import Typography from '@/components/Typography';
import images from '@/config/images';
import KioskConfig from '@/config/KioskConfig';
import {MainStackParamList} from '@/navigation';
import {scale, verticalScale} from '@/utils/scale';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React from 'react';
import {StyleSheet, View, ImageSourcePropType} from 'react-native';
import FastImage from 'react-native-fast-image';

interface ActionButton {
  id: number;
  title?: string;
  onPress: () => void;
  image?: ImageSourcePropType;
  brand?: string;
}

const SelectBrandsPadel = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();

  const actionButtons: ActionButton[] = [
    {
      id: 1,
      onPress: () => navigation.navigate('PadelList', {brand: 'dunlop'}),
      image: images.dunlop,
      brand: 'dunlop',
    },
    {
      id: 2,
      onPress: () => navigation.navigate('PadelList', {brand: 'adidas'}),
      image: images.adidas,
      brand: 'adidas',
    },
    {
      id: 3,
      onPress: () => navigation.navigate('PadelList', {brand: 'babolat'}),
      image: images.babolat,
      brand: 'babolat',
    },
    {
      id: 4,
      onPress: () => navigation.navigate('PadelList', {brand: 'nox'}),
      image: images.nox,
      brand: 'nox',
    },
    {
      id: 5,
      onPress: () => navigation.navigate('PadelList', {brand: 'bullaPadel'}),
      image: images.bullpadel,
      brand: 'bullaPadel',
    },
  ];

  return (
    <GradientBackground>
      <View style={[styles.container]}>
        <View style={[styles.contentContainer]}>
          <Typography
            variant="faqAnswer"
            color={KioskConfig.theme.colors.white}>
            Select brand
          </Typography>
          <View style={[styles.buttonContainer]}>
            {actionButtons.map(data => (
              <CustomButton
                onPress={data.onPress}
                key={data.id}
                style={styles.btnStyle}
                buttonContentContainerStyle={styles.buttonContentContainer}
                brand={data.brand}
              />
            ))}
          </View>
        </View>
        <View style={styles.imageContainer}>
          <FastImage
            source={images.padel}
            style={styles.image}
            resizeMode="contain"
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1440),
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    margin: 'auto',
  },
  btnStyle: {
    width: scale(594),
    height: verticalScale(104),
    justifyContent: 'center',
    alignItems: 'flex-start',
  },

  contentContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },

  buttonContainer: {
    display: 'flex',
    gap: scale(20),
    marginTop: 20,
  },
  image: {
    width: scale(600),
    height: verticalScale(1090),
  },
  buttonContentContainer: {
    gap: 25,
    alignItems: 'center',
  },
  imageContainer: {
    flex: 1,
    position: 'absolute',
    right: 0,
  },
});

export default SelectBrandsPadel;
