import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import Typography from './Typography';
import Icon from './Icon';
import {moderateScale, verticalScale} from '@/utils/scale';
import KioskConfig from '@/config/KioskConfig';

interface CounterProps {
  value?: number;
  label?: string | null;
  onDecrement?: () => void;
  onIncrement?: () => void;
  minValue?: number;
  maxValue?: number;
  containerStyle?: StyleProp<ViewStyle>;
  leftButtonStyle?: StyleProp<ViewStyle>;
  rightButtonStyle?: StyleProp<ViewStyle>;
  labelStyle?: StyleProp<TextStyle>;
}

const Counter = ({
  value: externalValue,
  label = null,
  onDecrement: externalDecrement,
  onIncrement: externalIncrement,
  minValue = 0,
  maxValue = Infinity,
  containerStyle,
  leftButtonStyle,
  rightButtonStyle,
  labelStyle,
}: CounterProps = {}) => {
  const [internalValue, setInternalValue] = useState(1);

  const value = externalValue !== undefined ? externalValue : internalValue;
  const isDecrementDisabled = value <= minValue;
  const isIncrementDisabled = value >= maxValue;

  const handleDecrement = () => {
    if (externalDecrement) {
      externalDecrement();
    } else {
      setInternalValue(prev => Math.max(minValue, prev - 1));
    }
  };

  const handleIncrement = () => {
    if (externalIncrement) {
      externalIncrement();
    } else {
      setInternalValue(prev => Math.min(maxValue, prev + 1));
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <TouchableOpacity
        style={[
          styles.button,
          isDecrementDisabled && styles.buttonDisabled,
          leftButtonStyle,
        ]}
        onPress={handleDecrement}
        disabled={isDecrementDisabled}>
        <Icon name="line" size={24} color="white" />
      </TouchableOpacity>

      <View style={styles.valueContainer}>
        <Typography
          variant="cocogooseRegular"
          style={[styles.valueText, labelStyle as TextStyle]}>
          {value}
          {label ? ` ${label}` : null}
        </Typography>
      </View>

      <TouchableOpacity
        style={[
          styles.button,
          isIncrementDisabled && styles.buttonDisabled,
          rightButtonStyle,
        ]}
        onPress={handleIncrement}
        disabled={isIncrementDisabled}>
        <Icon name="create-group" size={24} color="white" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  button: {
    width: 35,
    height: 35,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'white',
  },
  buttonDisabled: {
    backgroundColor: 'transparent',
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 24,
  },
  valueContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  valueText: {
    color: KioskConfig.theme.colors.white,
    fontSize: moderateScale(36),
    fontWeight: 'bold',
    lineHeight: verticalScale(40),
  },
});

export default Counter;
