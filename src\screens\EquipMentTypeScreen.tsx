import React, {useRef, useState} from 'react';
import {StyleSheet, View, TouchableOpacity, Dimensions} from 'react-native';
import GradientBackground from '../components/GradientBackground';
import images from '../config/images';
import {scale, verticalScale} from '@/utils/scale';
import Carousel from 'react-native-reanimated-carousel';
import FastImage from 'react-native-fast-image';
const EquipMentTypeScreen: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const {width: screenWidth} = Dimensions.get('window');
  const carouselRef = useRef<any>(null);

  const demoData = [
    {
      id: '1',
      component: (
        <View style={styles.ballImageContainer}>
          <FastImage source={images.ball2} style={styles.ballImage} />
        </View>
      ),
    },
    {
      id: '2',
      component: (
        <View style={styles.ballTechnology}>
          <FastImage source={images.ballTechnology} style={styles.ballImage} />
        </View>
      ),
    },
    {
      id: '3',
      component: (
        <View style={styles.ballManufacturing}>
          <FastImage
            source={images.ballManufacturingProcess}
            style={styles.ballImage}
          />
        </View>
      ),
    },
    {
      id: '4',
      component: (
        <View style={styles.graphicTraining}>
          <FastImage
            source={images.graphicTraining}
            style={styles.graphicTrainingImg}
          />
        </View>
      ),
    },
  ];

  const handleNext = () => {
    if (currentIndex < demoData.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      carouselRef.current?.scrollTo({index: nextIndex, animated: true});
    }
  };

  const handleBack = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      carouselRef.current?.scrollTo({index: prevIndex, animated: true});
    }
  };

  const renderCarouselWithNavigation = () => (
    <View style={styles.carouselWrapper}>
      {currentIndex !== 0 && (
        <TouchableOpacity
          style={[
            styles.navButton,
            currentIndex === 0 && styles.navButtonDisabled,
          ]}
          onPress={handleBack}
          disabled={currentIndex === 0}>
          <FastImage source={images.leftArrow} style={styles.navButtonImage} />
        </TouchableOpacity>
      )}

      <Carousel
        ref={carouselRef}
        loop={false}
        width={screenWidth * 0.9}
        height={screenWidth * 0.9}
        autoPlay={false}
        data={demoData}
        scrollAnimationDuration={1000}
        onSnapToItem={index => setCurrentIndex(index)}
        renderItem={({item}) => (
          <TouchableOpacity activeOpacity={1} style={styles.gridContainer}>
            {item.component}
          </TouchableOpacity>
        )}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 0.9,
          parallaxScrollingOffset: 50,
        }}
      />

      {currentIndex !== demoData.length - 1 && (
        <TouchableOpacity
          style={[
            styles.navButton,
            currentIndex === demoData.length - 1 && styles.navButtonDisabled,
          ]}
          onPress={handleNext}
          disabled={currentIndex === demoData.length - 1}>
          <FastImage source={images.rightArrow} style={styles.navButtonImage} />
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <GradientBackground useGradient={false} backgroundColor="#000000">
      {renderCarouselWithNavigation()}
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  carouselWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
  gridContainer: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  ballImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  graphicTraining: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  ballImageContainer: {
    width: scale(1040.02),
    height: verticalScale(895.64),
    margin: 'auto',
  },
  ballTechnology: {
    width: scale(1529),
    height: verticalScale(828.43),
    margin: 'auto',
  },
  ballManufacturing: {
    width: scale(1374),
    height: verticalScale(957.15),
    margin: 'auto',
  },
  graphicTrainingImg: {
    width: scale(1374),
    height: verticalScale(957.15),
    margin: 'auto',
  },
  navButton: {
    padding: 10,
    borderRadius: 50,
  },
  navButtonImage: {
    width: 40,
    height: 40,
  },
  navButtonDisabled: {
    opacity: 0.4,
  },
});

export default EquipMentTypeScreen;
