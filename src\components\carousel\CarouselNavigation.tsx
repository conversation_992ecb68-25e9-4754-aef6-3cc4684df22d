import React from 'react';
import {StyleSheet, Pressable} from 'react-native';
import Animated from 'react-native-reanimated';
import ArrowLeft from '../ArrowLeft';
import ArrowRight from '../ArrowRight';

interface CarouselNavigationProps {
  isFirstItem: boolean;
  isLastItem: boolean;
  onPrevious: () => void;
  onNext: () => void;
}

const CarouselNavigation: React.FC<CarouselNavigationProps> = ({
  isFirstItem,
  isLastItem,
  onPrevious,
  onNext,
}) => {
  return (
    <Animated.View style={styles.buttonRow}>
      <Pressable
        disabled={isFirstItem}
        onPress={onPrevious}
        style={({pressed}) => [
          styles.navButton,
          pressed && styles.navButtonPressed,
        ]}>
        <Animated.View>
          <ArrowLeft
            stroke={isFirstItem ? '#cbd5e1' : 'black'} // bg-gray-400
          />
        </Animated.View>
      </Pressable>

      <Pressable
        disabled={isLastItem}
        onPress={onNext}
        style={({pressed}) => [
          styles.navButton,
          pressed && styles.navButtonPressed,
        ]}>
        <Animated.View>
          <ArrowRight
            stroke={isLastItem ? '#cbd5e1' : 'black'} // bg-gray-400
          />
        </Animated.View>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  buttonRow: {
    paddingHorizontal: 16,
    flexDirection: 'row',
    paddingTop: 20,
    justifyContent: 'center',
  },
  navButton: {
    marginHorizontal: 8,
    padding: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  navButtonPressed: {
    backgroundColor: '#f3f4f6', // gray-100
    borderRadius: 12,
  },
});

export default CarouselNavigation;
