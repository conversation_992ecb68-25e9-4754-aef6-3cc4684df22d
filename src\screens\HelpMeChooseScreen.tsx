import React from 'react';
import GradientBackground from '../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import CustomButton from '../components/CustomButton';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {ballDetailButtonConfigs, BallsData} from '@/utils/staticData';
import {useLanguageStore} from '@/store/languageStore';

const HelpMeChooseScreen = ({route}: {route: any}) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {from} = route.params;
  const {t} = useLanguageStore();
  const handleNavigate = (config: any) => {
    if (config.screenName === 'EquipMentType') {
      navigation.navigate(config.screenName, {
        from: from,
      });
    } else {
      navigation.navigate(config.screenName, {
        ballData: BallsData[0],
      });
    }
  };

  return (
    <GradientBackground>
      <View style={styles.container}>
        <Typography variant="faqAnswer" color={KioskConfig.theme.colors.white}>
          Select
        </Typography>
        <View style={styles.buttonContainer}>
          {ballDetailButtonConfigs.map((config, index) => (
            <CustomButton
              key={index}
              text={t(config.text)}
              style={styles.button}
              onPress={() => handleNavigate(config)}
            />
          ))}
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 55,
    marginTop: 40,
  },
  titleContainer: {
    alignSelf: 'flex-start',
  },
  button: {
    width: 520,
    height: 172,
  },
});
export default HelpMeChooseScreen;
