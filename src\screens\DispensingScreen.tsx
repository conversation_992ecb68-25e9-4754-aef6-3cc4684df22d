import React, {useEffect, useRef} from 'react';
import GradientBackground from '../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {RouteProp} from '@react-navigation/native';
import {useLanguageStore} from '@/store/languageStore';
type DispensingScreenProps = {
  route: RouteProp<MainStackParamList, 'Dispensing'>;
};

const DispensingScreen = ({route}: DispensingScreenProps) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {from} = route.params || {};
  const {t} = useLanguageStore();
  const isRendered = useRef(true);
  console.log('from=====>>>>>', from);

  useEffect(() => {
    return () => {
      isRendered.current = false;
    };
  }, []);

  useEffect(() => {
    const timeOut = setTimeout(() => {
      if (isRendered.current) {
        navigation.navigate('SomethingElse', {from: from});
      }
    }, 3000);

    return () => {
      clearTimeout(timeOut);
    };
  }, [navigation]);

  return (
    <GradientBackground>
      <View style={styles.container}>
        <Typography
          variant="dispensingText"
          color={KioskConfig.theme.colors.text.highlight}
          align="center">
          {from === 'racquet'
            ? t('dispensing.racquets')
            : from === 'padel'
              ? t('dispensing.bat')
              : t('dispensing.balls')}{' '}
          {t('dispensing.dispensingBelow')}
        </Typography>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timer: {
    position: 'absolute',
    bottom: 260,
  },
});
export default DispensingScreen;
