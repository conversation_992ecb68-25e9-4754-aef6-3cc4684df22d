/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  useRef,
  useEffect,
  useState,
  useMemo,
  useCallback,
  memo,
} from 'react';
import {
  Animated,
  Dimensions,
  Image,
  StyleSheet,
  View,
  TouchableWithoutFeedback,
  InteractionManager,
} from 'react-native';
import GradientBackground from '../components/GradientBackground';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {scale, verticalScale} from '@/utils/scale';
import images from '@/config/images';
import WelcomeToGoRactModal from '@/components/WelcomeToGoRactModal';
import animation from '@/config/animation';
import LottieView from 'lottie-react-native';
import {useLanguageStore} from '@/store/languageStore';

const {width, height} = Dimensions.get('window');
const BALL_SIZES = [scale(100), scale(120), scale(140), scale(180)];

const BALL_TYPES = {
  AO: {type: 'AO', image: images.AOBall, AOBalls: true},
  ATP: {type: 'ATP', image: images.ATPBall, AOBalls: false},
  STAGE2: {type: 'STAGE2', image: images.Stage2Ball, AOBalls: false},
  GRAND_PRIX: {type: 'GRAND_PRIX', image: images.GrandPrixBall, AOBalls: false},
};

const BALLS_CONFIG = [
  {...BALL_TYPES.ATP, position: {x: width * 0.1, y: height * -0.1}},
  {...BALL_TYPES.AO, position: {x: width * 0.3, y: height * -0.15}},
  {...BALL_TYPES.ATP, position: {x: width * 0.55, y: height * -0.05}},
  {...BALL_TYPES.AO, position: {x: width * 0.85, y: height * -0.15}},
  {...BALL_TYPES.GRAND_PRIX, position: {x: width * 0.9, y: height * -0.25}},
  {...BALL_TYPES.GRAND_PRIX, position: {x: width * 0.15, y: height * -0.3}},
  {...BALL_TYPES.STAGE2, position: {x: width * 0.4, y: height * -0.35}},
  {...BALL_TYPES.AO, position: {x: width * 0.2, y: height * -0.5}},
  {...BALL_TYPES.ATP, position: {x: width * 0.9, y: height * -0.55}},
  {...BALL_TYPES.AO, position: {x: width * 0.5, y: height * -0.6}},
  {...BALL_TYPES.STAGE2, position: {x: width * 0.7, y: height * -0.7}},
  {...BALL_TYPES.GRAND_PRIX, position: {x: width * 0.5, y: height * -0.8}},
  {...BALL_TYPES.AO, position: {x: width * 0.8, y: height * -0.75}},
  {...BALL_TYPES.AO, position: {x: width * 0.1, y: height * -0.7}},
  {...BALL_TYPES.STAGE2, position: {x: width * 0.3, y: height * -0.85}},
];

// Pre-load Lottie animation to improve performance
const cachedAnimation = animation.ballBlasting;

const BallItem = memo(
  ({
    ball,
    onPress,
  }: {
    ball: {
      id: number;
      type: string;
      position: {x: number; y: number};
      size: number;
      image: any;
      AOBalls: boolean;
      duration: number;
      frozen: boolean;
      opacity: Animated.Value;
    };
    onPress: (
      id: number,
      tapped?: boolean,
      position?: {x: number; y: number},
    ) => void;
  }) => {
    const translateY = useRef(new Animated.Value(0)).current;
    const animationRef = useRef<Animated.CompositeAnimation | null>(null);
    const [stopped, setStopped] = useState(false);

    useEffect(() => {
      if (ball.frozen) return;

      const timer = setTimeout(() => {
        animationRef.current = Animated.timing(translateY, {
          toValue: 3000,
          duration: ball.duration,
          useNativeDriver: true,
        });

        animationRef.current.start(({finished}) => {
          if (finished && !stopped) {
            onPress(ball.id, false);
          }
        });
      }, 1000);

      return () => {
        clearTimeout(timer);
        animationRef.current?.stop();
      };
    }, [ball.frozen, ball.id, ball.duration, onPress, stopped, translateY]);

    const handleTap = useCallback(() => {
      if (animationRef.current) {
        animationRef.current.stop();
      }

      translateY.stopAnimation(currentValue => {
        const finalY = ball.position.y + currentValue;

        // Fade out the ball
        Animated.timing(ball.opacity, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }).start(() => {
          onPress(ball.id, true, {
            x: ball.position.x,
            y: finalY,
          });
        });

        setStopped(true);
      });
    }, [
      ball.id,
      ball.position.x,
      ball.position.y,
      ball.opacity,
      onPress,
      translateY,
    ]);

    return (
      <TouchableWithoutFeedback
        onPress={handleTap}
        disabled={ball.type !== 'AO'}>
        <Animated.View
          style={[
            {
              position: 'absolute',
              left: ball.position.x,
              top: ball.position.y,
              width: ball.size,
              height: ball.size,
              transform: [{translateY}],
              opacity: ball.opacity,
            },
          ]}>
          <Image
            source={ball.image}
            style={{width: '100%', height: '100%', resizeMode: 'contain'}}
          />
        </Animated.View>
      </TouchableWithoutFeedback>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if these specific props change
    return (
      prevProps.ball.id === nextProps.ball.id &&
      prevProps.ball.frozen === nextProps.ball.frozen
    );
  },
);

// Add display name to fix linter error
BallItem.displayName = 'BallItem';

// Memoize the LottieView component to prevent unnecessary re-renders
const OptimizedLottieView = memo(
  ({
    source,
    style,
    onAnimationFinish,
  }: {
    source: any;
    style: any;
    onAnimationFinish: () => void;
  }) => {
    const lottieRef = useRef<LottieView>(null);

    useEffect(() => {
      // Small delay to ensure UI thread is not blocked
      const timer = setTimeout(() => {
        if (lottieRef.current) {
          lottieRef.current.play();
        }
      }, 50);

      return () => clearTimeout(timer);
    }, []);

    return (
      <LottieView
        ref={lottieRef}
        source={source}
        autoPlay={false}
        loop={false}
        onAnimationFinish={onAnimationFinish}
        style={style}
        speed={1.0}
        renderMode="HARDWARE"
        resizeMode="cover"
      />
    );
  },
);

// Add display name to fix linter error
OptimizedLottieView.displayName = 'OptimizedLottieView';

// Memoized component for Lottie animation
const LottieBallAnimation = memo(
  ({
    lottieBall,
    onAnimationFinish,
  }: {
    lottieBall: {
      id: number;
      position: {x: number; y: number};
      size: number;
    };
    onAnimationFinish: () => void;
  }) => {
    return (
      <View
        pointerEvents="none"
        style={{
          position: 'absolute',
          left: lottieBall.position.x - 100,
          top: lottieBall.position.y,
          width: scale(lottieBall.size + 50),
          height: verticalScale(lottieBall.size + 50),
        }}>
        <OptimizedLottieView
          source={cachedAnimation}
          onAnimationFinish={onAnimationFinish}
          style={{width: '100%', height: '100%'}}
        />
      </View>
    );
  },
);

LottieBallAnimation.displayName = 'LottieBallAnimation';

const TimerScreen = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const isFocused = useIsFocused();
  const {t} = useLanguageStore();
  const isMounted = useRef(true);
  const [ballsCompleted, setBallsCompleted] = useState(0);
  const [userTapped, setUserTapped] = useState(false);
  const [visible, setVisible] = useState(false);

  const [lottieBall, setLottieBall] = useState<
    {id: number; position: {x: number; y: number}; size: number} | undefined
  >(undefined);

  const initialBalls = useMemo(
    () =>
      BALLS_CONFIG.map((ball, index) => ({
        ...ball,
        id: index + 1,
        size: BALL_SIZES[Math.floor(Math.random() * BALL_SIZES.length)],
        // Duration now between 5000ms (5s) and 7000ms (7s)
        duration: Math.floor(Math.random() * 2000) + 5000,
        frozen: false,
        opacity: new Animated.Value(1),
      })),
    [],
  );

  const [balls] = useState(initialBalls);

  useEffect(() => {
    if (ballsCompleted === BALLS_CONFIG.length) {
      if (!userTapped) {
        setTimeout(() => {
          if (isMounted.current && isFocused) {
            navigation.navigate('Congratulations');
          }
        }, 500);
      }
    }
  }, [ballsCompleted, userTapped, navigation, isFocused]);

  const handleBallPress = useCallback(
    (id: number, tapped?: boolean, position?: {x: number; y: number}) => {
      const ball = balls.find(b => b.id === id);
      if (!ball) return;

      // Instead of removing the ball, we'll keep it in the array but mark it as tapped
      if (tapped && position) {
        // Use InteractionManager to ensure UI is responsive during animation setup
        InteractionManager.runAfterInteractions(() => {
          setUserTapped(true);
          setLottieBall({
            id: ball.id,
            position,
            size: ball.size,
          });
        });
      } else {
        setBallsCompleted(prev => prev + 1);
      }
    },
    [balls],
  );

  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  const onLottieAnimationFinish = useCallback(() => {
    if (!lottieBall) return;

    // Use InteractionManager to ensure UI thread isn't blocked
    InteractionManager.runAfterInteractions(() => {
      setBallsCompleted(prev => prev + 1);
      setLottieBall(undefined);
      setVisible(true);
    });
  }, [lottieBall]);

  const handleModalClose = useCallback(() => {
    setVisible(false);
    navigation.navigate('Congratulations');
  }, [navigation]);

  // Render individual ball with memoization to prevent unnecessary re-renders
  const renderBall = useCallback(
    (ball: {
      id: number;
      type: string;
      position: {x: number; y: number};
      size: number;
      image: any;
      AOBalls: boolean;
      duration: number;
      frozen: boolean;
      opacity: Animated.Value;
    }) => {
      // Don't render the ball if it's currently being animated with Lottie
      if (lottieBall?.id === ball.id) {
        return null;
      }

      return <BallItem key={ball.id} ball={ball} onPress={handleBallPress} />;
    },
    [lottieBall, handleBallPress],
  );

  return (
    <GradientBackground>
      <View style={styles.container}>
        {balls.map(renderBall)}

        {lottieBall && (
          <LottieBallAnimation
            lottieBall={lottieBall}
            onAnimationFinish={onLottieAnimationFinish}
          />
        )}

        <View style={styles.instructionContainer}>
          <Typography
            variant="timerText"
            color={KioskConfig.theme.colors.white}
            style={styles.timerText}
            align="center">
            {t('timer.title')}
          </Typography>
        </View>

        <WelcomeToGoRactModal visible={visible} onClose={handleModalClose} />
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  instructionContainer: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timerText: {
    textAlign: 'center',
  },
});

export default memo(TimerScreen);
