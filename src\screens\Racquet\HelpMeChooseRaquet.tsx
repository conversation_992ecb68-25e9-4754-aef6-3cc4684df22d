import React, {useState} from 'react';
import GradientBackground from '../../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import images from '@/config/images';
import {scale, verticalScale} from '@/utils/scale';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import ToggleTab from '@/components/ToggleTab';
import FastImage from 'react-native-fast-image';
import {useLanguageStore} from '@/store/languageStore';

const LeftHandImages = [
  {
    id: 1,
    img: images.hand1,
    size: `4 1/8"`,
    style: {
      width: scale(376.65),
      height: verticalScale(412.15),
    },
  },
  {
    id: 2,
    img: images.hand2,
    size: `4 1/4"`,
    style: {
      width: scale(388.09),
      height: verticalScale(424.67),
    },
  },
  {
    id: 3,
    img: images.hand3,
    size: `4 3/8"`,
    style: {
      width: scale(399.49),
      height: verticalScale(437.15),
    },
  },
];

const RightHandImages = [
  {
    id: 1,
    img: images.hand1,
    size: `4 1/8"`,
    style: {
      transform: [{scaleX: -1}],
      width: scale(376.65),
      height: verticalScale(412.15),
    },
  },
  {
    id: 2,
    img: images.hand2,
    size: `4 1/4"`,
    style: {
      transform: [{scaleX: -1}],
      width: scale(388.09),
      height: verticalScale(424.67),
    },
  },
  {
    id: 3,
    img: images.hand3,
    size: `4 3/8"`,
    style: {
      transform: [{scaleX: -1}],
      width: scale(399.49),
      height: verticalScale(437.15),
    },
  },
];

const HelpMeChooseRaquet = () => {
  const {t} = useLanguageStore();
  const tabs = [
    t('helpMeChooseRacquet.leftHanded'),
    t('helpMeChooseRacquet.rightHanded'),
  ];
  const [selectedTab, setSelectedTab] = useState(tabs[0]);
  return (
    <GradientBackground>
      <View style={styles.container}>
        <View style={styles.handImageContainer}>
          {(selectedTab === tabs[0] ? LeftHandImages : RightHandImages).map(
            data => (
              <View key={data?.id}>
                <FastImage
                  source={data?.img}
                  style={data?.style}
                  resizeMode="contain"
                />
                <Typography
                  variant="gripSize"
                  align="center"
                  color={KioskConfig.theme.colors.white}
                  style={styles.gripSize}>
                  {data?.size}
                </Typography>
              </View>
            ),
          )}
        </View>
        <View style={styles.textContainer}>
          <Typography
            variant="gripSize"
            align="center"
            color={KioskConfig.theme.colors.white}
            style={styles.gripSizeText}>
            {t('helpMeChooseRacquet.description')}
          </Typography>
        </View>
        <View style={styles.toggleTabContainer}>
          <ToggleTab
            containerStyle={styles.toggleTab}
            tabs={tabs}
            getSelectedTab={tab => {
              setSelectedTab(tab);
            }}
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 'auto',
  },
  gripSize: {
    marginTop: 30,
  },
  gripSizeText: {
    marginTop: 50,
    textAlign: 'center',
  },
  handImageContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
  textContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  toggleTab: {
    marginTop: 10,
    width: scale(201),
    height: verticalScale(26),
  },
  toggleTabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: verticalScale(10),
  },
});
export default HelpMeChooseRaquet;
