import React, {useState, useRef} from 'react';
import {View, StyleSheet, TouchableOpacity, Dimensions} from 'react-native';
import KioskConfig from '../config/KioskConfig';
import {appDemoSteps} from '../config/staticData';
import Header from '../components/Header';
import Icon from '@components/Icon';
import Carousel from 'react-native-reanimated-carousel';
import FastImage from 'react-native-fast-image';

interface DemoItem {
  id: number;
  title: string;
  image: any;
}

const demoData: DemoItem[] = appDemoSteps;

const AppDemo = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const {width: screenWidth, height: screenHeight} = Dimensions.get('window');
  const carouselRef = useRef<any>(null);

  const handleNext = () => {
    if (currentIndex < demoData.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      carouselRef.current?.scrollTo({index: nextIndex, animated: true});
    }
  };

  const handleBack = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      carouselRef.current?.scrollTo({index: prevIndex, animated: true});
    }
  };

  const renderItem = ({item}: {item: DemoItem}) => {
    return (
      <TouchableOpacity activeOpacity={1} onPress={handleNext}>
        <FastImage
          source={item.image}
          style={styles.image}
          resizeMode="contain"
        />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Header showBackButton={true} handleBackPress={handleBack} />
      <View style={styles.content}>
        <TouchableOpacity
          style={[
            styles.navButton,
            styles.leftNavButton,
            currentIndex === 0 && styles.navButtonDisabled,
          ]}
          onPress={handleBack}
          disabled={currentIndex === 0}>
          <Icon
            name="Left-chevron"
            size={40}
            color={KioskConfig.theme.colors.white}
          />
        </TouchableOpacity>
        <Carousel
          ref={carouselRef}
          loop={false}
          width={screenWidth * 0.92}
          height={screenHeight * 0.98}
          autoPlay={false}
          data={demoData}
          scrollAnimationDuration={1000}
          onSnapToItem={index => setCurrentIndex(index)}
          renderItem={({item}) => renderItem({item})}
          mode="parallax"
          modeConfig={{
            parallaxScrollingScale: 0.9,
            parallaxScrollingOffset: 50,
          }}
        />

        <TouchableOpacity
          style={[
            styles.navButton,
            styles.rightNavButton,
            currentIndex === demoData.length - 1 && styles.navButtonDisabled,
          ]}
          onPress={handleNext}
          disabled={currentIndex === demoData.length - 1}>
          <Icon
            name="Right-chevron"
            size={40}
            color={KioskConfig.theme.colors.white}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: KioskConfig.theme.colors.black,
    padding: 20,
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  slideContainer: {
    width: Dimensions.get('window').width - 140,
    height: '95%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  flatListContent: {
    paddingLeft: 0,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  navButton: {
    padding: 10,
    borderRadius: 50,
  },
  leftNavButton: {
    marginRight: 10,
  },
  rightNavButton: {
    marginLeft: 10,
  },
  navButtonImage: {
    width: 40,
    height: 40,
  },
  navButtonDisabled: {
    opacity: 0.4,
  },
});

export default AppDemo;
