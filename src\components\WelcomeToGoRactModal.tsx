import React from 'react';
import {View, Modal, StyleSheet, TouchableOpacity} from 'react-native';
import Typography from './Typography';
import {scale} from '@/utils/scale';

interface WelcomeToGoRactModalProps {
  visible: boolean;
  onClose: () => void;
}

const WelcomeToGoRactModal: React.FC<WelcomeToGoRactModalProps> = ({
  visible,
  onClose,
}) => {
  return (
    <Modal
      transparent
      animationType="fade"
      visible={visible}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.appliedContainer}>
            <Typography variant="appliedText">Applied</Typography>
            <Typography variant="appliedText2">WELCOMETOGORAQT</Typography>
          </View>

          <View style={styles.messageContainer}>
            <Typography variant="offText">Enjoy 20% OFF on this</Typography>
            <Typography variant="offText2" color="#808080">
              Keep shopping with GoRaqt to save with every order.
            </Typography>

            <View style={styles.divider} />

            <TouchableOpacity style={styles.okayButton} onPress={onClose}>
              <Typography variant="appliedText">Okay</Typography>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    maxWidth: 515,
    borderRadius: 12,
    overflow: 'hidden',
  },
  appliedContainer: {
    backgroundColor: '#e6ff54',
    display: 'flex',
    padding: 28.45,
  },
  messageContainer: {
    backgroundColor: 'white',
    padding: 20,
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 20,
  },
  okayButton: {
    alignItems: 'center',
  },
});

export default WelcomeToGoRactModal;
