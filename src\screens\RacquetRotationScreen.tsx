import React, {useEffect, useState, useRef} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  PanResponder,
} from 'react-native';
import GradientBackground from '../components/GradientBackground';
import images from '../config/images';
// import Icon from '../components/Icon';
import {moderateScale, scale, verticalScale} from '@/utils/scale';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {MainStackParamList} from '@/navigation';
import {RouteProp, useNavigation} from '@react-navigation/native';
import LottieView from 'lottie-react-native';
import animation from '@/config/animation';
import {StackNavigationProp} from '@react-navigation/stack';
import FastImage from 'react-native-fast-image';
import {useReservation} from '@/context/ReservationContext';
import {useLanguageStore} from '@/store/languageStore';

export type RacquetRotationScreenProps = {
  route: RouteProp<MainStackParamList, 'RacquetRotation'>;
};
const RacquetRotationScreen = ({route}: RacquetRotationScreenProps) => {
  const {item, selectedOption, from} = route.params || {};
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  const {setFlowType} = useReservation();
  const isRendered = useRef(true);
  const [animationState, setAnimationState] = useState({
    status: 'paused',
    speed: 1,
  });

  const [direction, setDirection] = useState<'up' | 'down' | null>(null);
  const [swipeDirection, setSwipeDirection] = useState<'up' | 'down' | null>(
    null,
  );

  const [showCongratulations, setShowCongratulations] = useState(false);

  // Create pan responder optimized for 32-inch touch display
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      // Very low threshold for large touch displays
      onMoveShouldSetPanResponder: (_, gestureState) =>
        Math.abs(gestureState.dy) > 2, // Extremely sensitive for touch displays
      onPanResponderMove: (_, gestureState) => {
        // Detect even slight vertical movements
        if ((gestureState.dy < -5 || gestureState.vy < -0.3) && !direction) {
          // Swipe up - very sensitive threshold
          setSwipeDirection('up');
          handleSwipe('up');
        } else if (
          (gestureState.dy > 5 || gestureState.vy > 0.3) &&
          !direction
        ) {
          // Swipe down - very sensitive threshold
          setSwipeDirection('down');
          handleSwipe('down');
        }
      },
      onPanResponderRelease: () => {
        setSwipeDirection(null);
      },
    }),
  ).current;

  // Handle swipe gesture
  const handleSwipe = (dir: 'up' | 'down') => {
    setDirection(dir);
    setAnimationState({
      status: 'play',
      speed: dir === 'up' ? 1 : -1,
    });
  };

  const handleUpPress = () => {
    setDirection('up');
    setAnimationState({
      status: 'play',
      speed: 1,
    });
  };

  const handleDownPress = () => {
    setDirection('down');
    setAnimationState({
      status: 'play',
      speed: -1,
    });
  };

  useEffect(() => {
    return () => {
      isRendered.current = false;
    };
  }, []);

  useEffect(() => {
    if (showCongratulations) {
      const timeOut = setTimeout(() => {
        if (isRendered.current) {
          setFlowType('CHECKOUT');

          // Create navigation parameters with default values to satisfy TypeScript
          const navParams = {
            from: from || '',
            item: {
              title: item?.title || '',
              description: item?.description || '',
              image: String(item?.image || ''),
              handleImage: item?.handleImage || 0,
            },
            selectedOption: selectedOption || '',
          };

          navigation.navigate('CheckOutOrReturnScreen', navParams);
          setAnimationState({
            status: 'paused',
            speed: 1,
          });
          setShowCongratulations(false);
        }
      }, 3000);

      return () => {
        clearTimeout(timeOut);
      };
    }
  }, [
    showCongratulations,
    navigation,
    from,
    item,
    selectedOption,
    setFlowType,
  ]);

  return (
    <>
      <GradientBackground useGradient>
        <View
          {...panResponder.panHandlers}
          style={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1, // Ensure it's above other elements but below UI controls
            // Remove border for cleaner fullscreen experience
            // borderWidth: 1,
            // borderColor: 'rgba(255,255,255,0.1)',
          }}>
          {animationState.status === 'paused' ? (
            <FastImage
              source={
                images.RotateRacquet || item?.handleImage || images.raqtHandle
              }
              style={styles.racquetImage}
              resizeMode="contain"
            />
          ) : (
            <View
              style={{
                width: scale(1861),
                height: verticalScale(950),
                position: 'absolute',
                top: -110,
                left: 0,
                right: 0,
                bottom: 0,
              }}>
              <LottieView
                source={animation.RacquetAnimationGreen}
                autoPlay
                loop
                speed={animationState.speed}
                style={{
                  width: '100%',
                  height: '100%',
                }}
              />
            </View>
          )}
        </View>
        <View style={styles.container}>
          <View style={styles.racquetContainer}>
            <View style={styles.directionButtonContainer}>
              <TouchableOpacity
                style={styles.directionButton}
                onPress={handleUpPress}
                activeOpacity={0.7}>
                {direction === 'up' ? (
                  <View style={styles.lottieView}>
                    <LottieView
                      source={animation.upButton}
                      autoPlay
                      loop={false}
                      onAnimationFinish={() => {
                        setDirection(null);
                        setTimeout(() => {
                          setShowCongratulations(true);
                        }, 2000);
                      }}
                      style={styles.directionButton}
                    />
                  </View>
                ) : (
                  <Typography variant="userTitle" style={styles.directionText}>
                    {t('racquetRotation.up')}
                  </Typography>
                )}
              </TouchableOpacity>
            </View>
            <View style={styles.directionButtonContainer}>
              <TouchableOpacity
                style={styles.directionButton}
                onPress={handleDownPress}
                activeOpacity={0.7}>
                {direction === 'down' ? (
                  <View style={styles.lottieView}>
                    <LottieView
                      source={animation.downButton}
                      autoPlay
                      loop={false}
                      onAnimationFinish={() => {
                        setDirection(null);
                        setTimeout(() => {
                          setShowCongratulations(true);
                        }, 2000);
                      }}
                      style={styles.directionButton}
                    />
                  </View>
                ) : (
                  <Typography variant="userTitle" style={styles.directionText}>
                    {t('racquetRotation.down')}
                  </Typography>
                )}
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.handContainer}>
            {/* <Icon
              name="arrow"
              size={155}
              color="#DFFC4F"
              style={styles.arrow}
            /> */}
            <LottieView
              source={animation.hand}
              autoPlay
              loop
              style={styles.hand}
            />
            <View style={styles.instructionContainer}>
              <Text style={styles.instructionText}>
                {t('racquetRotation.instruction')}
              </Text>
            </View>
          </View>
        </View>
      </GradientBackground>
      {showCongratulations && (
        <View style={styles.overLayContainer}>
          <Typography variant="userTitle" style={styles.congratulationText}>
            {t('racquetRotation.congratulation')}
          </Typography>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  overLayContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: KioskConfig.theme.colors.black,
    opacity: 0.8,
    justifyContent: 'center',
    zIndex: 10,
  },
  hand: {
    width: scale(258),
    height: verticalScale(278),
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    // alignItems: 'center',
  },
  lottieView: {
    // width: scale(236),
    // height: verticalScale(236),
    // justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 10,
    overflow: 'hidden',
  },
  directionButtonContainer: {},
  racquetContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-evenly',
    // gap: 20,
    // backgroundColor: 'red',
    // width: '80%',
    flex: 1,
    // marginBottom: 100,
  },
  directionButton: {
    backgroundColor: KioskConfig.theme.colors.white,
    width: scale(236),
    height: verticalScale(236),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 18,
  },
  directionText: {
    fontSize: moderateScale(56),
    fontWeight: '900',
    color: KioskConfig.theme.colors.black,
  },
  racquetImage: {
    width: scale(1861),
    height: verticalScale(950),
    position: 'absolute',
    top: -110,
    left: 0,
    right: 0,
    bottom: 0,
  },
  swipeContainer: {
    alignItems: 'center',
  },
  handContainer: {
    // flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    // marginTop: 20,
    flex: 0.75,
  },
  arrow: {
    position: 'absolute',
    top: 80,
  },
  instructionContainer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
  },
  congratulationText: {
    color: KioskConfig.theme.colors.white,
    fontSize: moderateScale(80),
    fontWeight: '900',
    textAlign: 'center',
    position: 'absolute',
    // bottom: 0,
    left: 0,
    right: 0,
    zIndex: 9999,
  },
  instructionText: {
    color: KioskConfig.theme.colors.black,
    fontSize: moderateScale(50),
    textAlign: 'center',
    // marginTop: 20,
  },
});

export default RacquetRotationScreen;
