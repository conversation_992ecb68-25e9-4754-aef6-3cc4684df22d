import React, {useState} from 'react';
import {View, StyleSheet, Dimensions, TouchableOpacity} from 'react-native';
import ReanimatedCarousel from './ReanimatedCarousel';
import GradientBackground from './GradientBackground';
import Typography from './Typography';
import KioskConfig from '@/config/KioskConfig';
import AdvancedCarouselExample from './AdvancedCarouselExample';
import images from '@/config/images';
import FastImage from 'react-native-fast-image';
import {moderateScale} from '@/utils/scale';

// Import images from your assets
// Replace these with your actual image imports
const demoImages = {
  image1: images.fullLogo,
  image2: images.fullLogo,
  image3: images.fullLogo,
};

const {width: screenWidth} = Dimensions.get('window');

const CarouselExample: React.FC = () => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  if (showAdvanced) {
    return (
      <View style={{flex: 1}}>
        <TouchableOpacity
          style={styles.switchButton}
          onPress={() => setShowAdvanced(false)}>
          <Typography variant="customButton" style={styles.switchButtonText}>
            Show Basic Example
          </Typography>
        </TouchableOpacity>
        <AdvancedCarouselExample />
      </View>
    );
  }
  // Example data for the carousel
  const carouselData = [
    {
      id: '1',
      image: demoImages.image1,
      title: 'Premium Equipment',
      description: 'High-quality sports equipment for professionals',
    },
    {
      id: '2',
      image: demoImages.image2,
      title: 'Advanced Technology',
      description: 'Featuring the latest innovations in sports gear',
    },
    {
      id: '3',
      image: demoImages.image3,
      title: 'Performance Focused',
      description: 'Designed to enhance your athletic performance',
    },
    {
      id: '4',
      component: (
        <View style={styles.customSlide}>
          <Typography variant="faqAnswer" style={styles.customTitle}>
            Custom Component Slide
          </Typography>
          <View style={styles.customContent}>
            <Typography variant="customButton" style={styles.customDescription}>
              This slide demonstrates how to use a custom component in the
              carousel. You can include any React Native components here.
            </Typography>
          </View>
        </View>
      ),
    },
  ];

  // Example of a custom render function (optional)
  const customRenderItem = (item: any, index: number) => {
    // Only apply custom rendering to specific slides
    // if (index === 2) {
    return (
      <View style={styles.specialSlide}>
        <FastImage
          source={item.image}
          style={styles.specialImage}
          resizeMode="contain"
        />
        <View style={styles.specialTextContainer}>
          <Typography variant="faqAnswer" style={styles.specialTitle}>
            {item.title}
          </Typography>
          <Typography variant="customButton" style={styles.specialDescription}>
            {item.description}
          </Typography>
        </View>
      </View>
    );
    // }

    // For other slides, return null to use the default renderer
    return null;
  };

  return (
    <GradientBackground>
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.switchButton}
          onPress={() => setShowAdvanced(true)}>
          <Typography variant="customButton" style={styles.switchButtonText}>
            Show Advanced Example
          </Typography>
        </TouchableOpacity>

        <Typography variant="faqAnswer" style={styles.pageTitle}>
          Reanimated Carousel Example
        </Typography>

        <View style={styles.carouselContainer}>
          <ReanimatedCarousel
            data={carouselData}
            width={screenWidth * 0.8}
            height={400}
            autoPlay={true}
            autoPlayInterval={5000}
            showArrows={true}
            showPagination={true}
            loop={true}
            style={styles.carousel}
            customRenderItem={customRenderItem}
            onSnapToItem={index => console.log('Current index:', index)}
          />
        </View>

        <View style={styles.infoContainer}>
          <Typography variant="customButton" style={styles.infoText}>
            This is an example of the ReanimatedCarousel component. It supports
            custom rendering, auto-play, navigation arrows, and pagination.
          </Typography>
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pageTitle: {
    fontSize: moderateScale(32),
    fontWeight: 'bold',
    marginBottom: 30,
    color: KioskConfig.theme.colors.white,
  },
  carouselContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  carousel: {
    marginVertical: 20,
  },
  infoContainer: {
    marginTop: 30,
    padding: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 10,
    width: '80%',
  },
  infoText: {
    textAlign: 'center',
    color: KioskConfig.theme.colors.white,
  },
  customSlide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 15,
    padding: 20,
  },
  customTitle: {
    fontSize: moderateScale(28),
    fontWeight: 'bold',
    color: KioskConfig.theme.colors.white,
    marginBottom: 20,
  },
  customContent: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 15,
    borderRadius: 10,
    width: '90%',
  },
  customDescription: {
    fontSize: moderateScale(18),
    textAlign: 'center',
    color: KioskConfig.theme.colors.white,
  },
  specialSlide: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: 'rgba(0, 100, 255, 0.1)',
    borderRadius: 15,
    padding: 20,
  },
  specialImage: {
    width: '40%',
    height: '80%',
  },
  specialTextContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
  },
  specialTitle: {
    fontSize: moderateScale(24),
    fontWeight: 'bold',
    color: KioskConfig.theme.colors.white,
    marginBottom: 15,
  },
  specialDescription: {
    fontSize: moderateScale(16),
    textAlign: 'center',
    color: KioskConfig.theme.colors.white,
  },
  switchButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
    zIndex: 10,
  },
  switchButtonText: {
    color: KioskConfig.theme.colors.white,
    fontWeight: 'bold',
  },
});

export default CarouselExample;
