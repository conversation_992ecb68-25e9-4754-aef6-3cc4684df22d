import React, {useState} from 'react';
import {
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import Typography from '../Typography';
import KioskConfig from '@/config/KioskConfig';
import GripRadio from './GripRadio';
import CustomButton from '../CustomButton';
import images from '@/config/images';
import RadioSelect from '../CRadioSelect';
import Counter from '../Counter';
import {scale, verticalScale} from '@/utils/scale';
import FastImage from 'react-native-fast-image';
import Icon from '../Icon';
import {getBrandName} from '@/utils/CommonFunctions';
import {useLanguageStore} from '@/store/languageStore';

interface GripSize {
  value: string;
  label: string;
}

interface Color {
  value: string;
  label: string;
}

interface ProductDetailsProps {
  title: string;
  description: string;
  containerStyle?: ViewStyle;
  btnContainerStyle?: ViewStyle;
  btnStyle?: ViewStyle;
  textStyle?: TextStyle;
  actionButtons: ActionButton[];
  gripSizes?: GripSize[];
  colors?: Color[];
  radioOptions?: RadioOption[];
  radioTitle?: string;
  showCounter?: boolean;
  counterTitle?: string;
  calendar?: () => React.ReactNode;
  helpMeChoosePress?: () => void;
  brand?: string;
}

interface RadioOption {
  label: string;
  value: string;
}

interface ActionButton {
  id: number;
  title: string;
  onPress?: () => void;
  icon?: string;
  highlighted?: boolean;
  dropdown?: boolean;
  items?: Array<{label: string; value: string}>;
  onSelectItem?: (item: {label: string; value: string}) => void;
}

const ProductDetails = (props: ProductDetailsProps) => {
  const {
    title,
    description,
    containerStyle,
    btnContainerStyle,
    btnStyle,
    textStyle,
    actionButtons,
    gripSizes = [],
    colors = [],
    radioOptions = [],
    radioTitle = '',
    showCounter = false,
    counterTitle = '',
    calendar,
    helpMeChoosePress,
    brand,
  } = props;
  const {t} = useLanguageStore();
  const [selectedGrip, setSelectedGrip] = useState<string>('');

  const [selectedColor, setSelectedColor] = useState<string>('');

  const [radioSelected, setRadioSelected] = useState<string>('');

  const [counterValue, setCounterValue] = useState<number>(0);

  return (
    <View style={[styles.container, containerStyle]}>
      <Icon
        name={getBrandName(brand)}
        size={brand === 'head' ? scale(30) : scale(41)}
        color="white"
      />
      <View style={styles.contentContainer}>
        <Typography
          variant="dunlopTitle"
          color={KioskConfig.theme.colors.white}>
          {title}
        </Typography>
        <Typography
          variant="dunlopDescription"
          style={styles.description}
          color={KioskConfig.theme.colors.white}>
          {description}
        </Typography>
      </View>
      {gripSizes?.length > 0 && (
        <View style={styles.gripContainer}>
          <Typography
            variant="RadioTitle"
            color={KioskConfig.theme.colors.white}
            style={styles.gripTitle}>
            {t('racquetDetails.chooseGripSize')}
          </Typography>
          <View style={styles.gripRadioContainer}>
            {gripSizes?.map(option => (
              <GripRadio
                key={option.value}
                label={option.label}
                selected={selectedGrip === option.value}
                onPress={() => setSelectedGrip(option.value)}
              />
            ))}
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={helpMeChoosePress}
              style={styles.helpMeChooseContainer}>
              <FastImage
                source={images.hello}
                style={styles.helloImage}
                resizeMode="contain"
              />
              <Typography
                variant="helpMeChoose"
                style={styles.helpMeChooseText}
                color={KioskConfig.theme.colors.white}>
                {t('racquetDetails.helpMeChoose')}
              </Typography>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {colors?.length > 0 && (
        <>
          <Typography
            variant="RadioTitle"
            color={KioskConfig.theme.colors.white}>
            {t('common.color')}
          </Typography>
          <View style={styles.colorContainer}>
            {colors?.map(option => (
              <TouchableOpacity
                key={option.value}
                onPress={() => {
                  setSelectedColor(option.value);
                }}
                activeOpacity={0.7}
                style={[
                  styles.colorView,
                  {
                    backgroundColor: option.value,
                    borderWidth: 3,
                    borderColor:
                      selectedColor === option.value
                        ? '#DFFC4F'
                        : KioskConfig.theme.colors.white,
                  },
                ]}
              />
            ))}
          </View>
        </>
      )}
      {radioOptions?.length > 0 && (
        <>
          <Typography
            variant="RadioTitle"
            color={KioskConfig.theme.colors.white}>
            {radioTitle}
          </Typography>
          <View style={styles.radioContainer}>
            {radioOptions?.map(option => (
              <RadioSelect
                key={option.value}
                label={option.label}
                selected={radioSelected === option.value}
                onPress={() => {
                  setRadioSelected(option.value);
                }}
              />
            ))}
          </View>
        </>
      )}
      {showCounter && (
        <>
          <Typography
            variant="counterLabel"
            style={{marginTop: 30}}
            color={KioskConfig.theme.colors.white}>
            {counterTitle}
          </Typography>
          <View style={styles.counterContainer}>
            <Counter
              value={counterValue}
              onDecrement={() => setCounterValue(prev => prev - 1)}
              onIncrement={() => setCounterValue(prev => prev + 1)}
            />
          </View>
        </>
      )}

      {calendar ? (
        <View style={{marginTop: scale(20)}}>{calendar()}</View>
      ) : null}
      <View
        style={[
          {gap: 20, marginTop: calendar ? scale(15) : scale(100)},
          btnContainerStyle,
        ]}>
        {actionButtons.map(data => (
          <CustomButton
            text={data.title}
            onPress={data.onPress}
            highlighted={
              data?.highlighted || !data?.highlighted
                ? data?.highlighted
                : data?.id === 1
            }
            key={data.id}
            style={[styles.button, btnStyle]}
            icon={data.icon}
            buttonContentContainerStyle={styles.buttonContentContainer}
            textStyle={textStyle}
            dropdown={data.dropdown}
            dropdownItems={data.items}
            onSelectItem={data.onSelectItem}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
  },
  dunlopImage: {
    width: scale(275),
    height: verticalScale(41),
    marginBottom: 25,
  },
  contentContainer: {
    alignItems: 'flex-start',
    marginTop: verticalScale(20),
  },
  description: {
    marginTop: 15,
    marginBottom: 30,
  },
  gripContainer: {marginBottom: 30},
  gripRadioContainer: {
    flexDirection: 'row',
    gap: 20,
  },
  colorContainer: {
    marginTop: 10,
    flexDirection: 'row',
    gap: 20,
  },
  colorView: {
    height: verticalScale(50),
    width: scale(50),
    borderRadius: 200,
  },

  buttonContentContainer: {
    gap: 25,
  },
  helpMeChooseContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
    flexDirection: 'row',
    gap: 10,
  },
  helloImage: {
    width: scale(35),
    height: verticalScale(35),
  },
  helpMeChooseText: {
    marginTop: 10,
  },

  radioContainer: {
    flexDirection: 'column',
    gap: 5,
  },
  counterContainer: {
    marginTop: 10,
  },
  button: {
    maxWidth: scale(400),
    height: verticalScale(75),
  },
  gripTitle: {
    marginBottom: 10,
  },
});

export default ProductDetails;
