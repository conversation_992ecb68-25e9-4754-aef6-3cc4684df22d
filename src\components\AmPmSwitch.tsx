import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import {FONTS} from '@/utils/fonts';
import KioskConfig from '@/config/KioskConfig';

interface AmPmSwitchProps {
  value: 'AM' | 'PM';
  onChange: (value: 'AM' | 'PM') => void;
  containerStyle?: ViewStyle;
}

const AmPmSwitch: React.FC<AmPmSwitchProps> = ({
  value,
  onChange,
  containerStyle,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <TouchableOpacity
        style={[styles.option, value === 'AM' && styles.activeOption]}
        onPress={() => onChange('PM')}>
        <Text
          style={[
            styles.optionText,
            value === 'AM' && styles.activeOptionText,
          ]}>
          AM
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.option, value === 'PM' && styles.activeOption]}
        onPress={() => onChange('AM')}>
        <Text
          style={[
            styles.optionText,
            value === 'PM' && styles.activeOptionText,
          ]}>
          PM
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: KioskConfig.theme.colors.lightGray,
    // margin: 5,
    padding: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  option: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    minWidth: 60,
  },
  activeOption: {
    backgroundColor: KioskConfig.theme.colors.lightGray,
  },
  optionText: {
    fontSize: 18,
    color: KioskConfig.theme.colors.black,
    fontFamily: FONTS.regular,
  },
  activeOptionText: {
    color: KioskConfig.theme.colors.black,
    fontFamily: FONTS.regular,
  },
});

export default AmPmSwitch;
