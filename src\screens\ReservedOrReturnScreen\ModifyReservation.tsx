import React, {useState, useRef} from 'react';
import {View, StyleSheet, TouchableOpacity, Animated} from 'react-native';
import GradientBackground from '../../components/GradientBackground';
import KioskConfig from '../../config/KioskConfig';
import Typography from '../../components/Typography';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '../../navigation';
import CustomButton from '@/components/CustomButton';
import images from '../../config/images';
import Counter from '@/components/Counter';
import {moderateScale, scale, verticalScale} from '@/utils/scale';

const ModifyReservation = () => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const [hours, setHours] = useState(0);
  const [days, setDays] = useState(0);
  const [selectedRacquet, setSelectedRacquet] = useState<
    'rac1' | 'rac2' | null
  >(null);

  // Animation values
  const rac1Scale = useRef(new Animated.Value(1)).current;
  const rac2Scale = useRef(new Animated.Value(1)).current;
  const rac1Position = useRef(new Animated.ValueXY({x: 0, y: 0})).current;
  const rac2Position = useRef(new Animated.ValueXY({x: -90, y: 0})).current;
  const rac1Opacity = useRef(new Animated.Value(1)).current;
  const rac2Opacity = useRef(new Animated.Value(1)).current;

  const handleRacquetPress = (racquet: 'rac1' | 'rac2') => {
    if (selectedRacquet === racquet) {
      // Reset to default state
      Animated.parallel([
        Animated.timing(rac1Scale, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(rac2Scale, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(rac1Position, {
          toValue: {x: 0, y: 0},
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(rac2Position, {
          toValue: {x: -90, y: 0},
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(rac1Opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(rac2Opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
      setSelectedRacquet(null);
    } else {
      // Expand the selected racquet and hide the other
      if (racquet === 'rac1') {
        // Red racquet selected - move it to front and make it larger
        Animated.parallel([
          Animated.timing(rac1Scale, {
            toValue: 1.2,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(rac1Position, {
            toValue: {x: 100, y: 0}, // Move it more to the right to center it
            duration: 300,
            useNativeDriver: true,
          }),
          // Make blue racquet smaller and move it behind
          Animated.timing(rac2Scale, {
            toValue: 0.85,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(rac2Position, {
            toValue: {x: -150, y: 0}, // Push it further back
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(rac2Opacity, {
            toValue: 0.7, // Instead of hiding completely, make it more transparent
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        // Blue racquet selected - move it to front and make it larger
        Animated.parallel([
          Animated.timing(rac2Scale, {
            toValue: 1.2,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(rac2Position, {
            toValue: {x: -30, y: 0}, // Move it more to the left to center it
            duration: 300,
            useNativeDriver: true,
          }),
          // Make red racquet smaller and move it behind
          Animated.timing(rac1Scale, {
            toValue: 0.85,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(rac1Position, {
            toValue: {x: -50, y: 0}, // Push it further back
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(rac1Opacity, {
            toValue: 0.7, // Instead of hiding completely, make it more transparent
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      }
      setSelectedRacquet(racquet);
    }
  };

  return (
    <GradientBackground>
      <View style={styles.container}>
        <View style={styles.leftSection}>
          <Typography variant="heading2" color={KioskConfig.theme.colors.white}>
            {selectedRacquet
              ? `${selectedRacquet === 'rac1' ? 'CX200' : 'SX300'} racquet selected`
              : 'Select racquet to change time'}
          </Typography>

          <View style={styles.timeSelectors}>
            <Counter
              value={hours}
              label="hour"
              onDecrement={() => setHours(prev => Math.max(0, prev - 1))}
              onIncrement={() => setHours(prev => Math.min(24, prev + 1))}
              minValue={0}
              maxValue={24}
              leftButtonStyle={styles.leftButton}
              rightButtonStyle={styles.leftButton}
              labelStyle={styles.label}
            />
            <Counter
              value={days}
              label="days"
              onDecrement={() => setDays(prev => Math.max(0, prev - 1))}
              onIncrement={() => setDays(prev => Math.min(30, prev + 1))}
              minValue={0}
              maxValue={30}
              leftButtonStyle={styles.leftButton}
              rightButtonStyle={styles.leftButton}
              labelStyle={styles.label}
            />
          </View>

          {selectedRacquet && (
            <View style={styles.buttonContainer}>
              <CustomButton
                text="Add to order"
                style={styles.actionButton}
                onPress={() => navigation.navigate('CheckOutOrReturnScreen')}
              />
              <CustomButton
                text="Confirm"
                style={styles.actionButton}
                onPress={() => navigation.navigate('SomethingElse')}
              />
            </View>
          )}

          {!selectedRacquet && (
            <Typography
              variant="body"
              color={KioskConfig.theme.colors.white}
              style={styles.instructionText}>
              Please tap on a racquet to select it
            </Typography>
          )}
        </View>

        <View
          style={[
            styles.rightSection,
            selectedRacquet ? styles.rightSectionSelected : null,
          ]}>
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => handleRacquetPress('rac1')}
            style={{
              zIndex: selectedRacquet === 'rac1' ? 2 : 1,
            }}>
            <Animated.Image
              source={images.dunlopCx200}
              style={[
                styles.rac1,
                {
                  transform: [
                    {scale: rac1Scale},
                    {translateX: rac1Position.x},
                    {translateY: rac1Position.y},
                  ],
                  opacity: rac1Opacity,
                  zIndex: selectedRacquet === 'rac1' ? 2 : 1,
                },
              ]}
              resizeMode="contain"
            />
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => handleRacquetPress('rac2')}
            style={{
              zIndex: selectedRacquet === 'rac2' ? 2 : 1,
            }}>
            <Animated.Image
              source={images.dunlopSx300}
              style={[
                styles.rac2,
                {
                  transform: [
                    {scale: rac2Scale},
                    {translateX: rac2Position.x},
                    {translateY: rac2Position.y},
                  ],
                  opacity: rac2Opacity,
                  marginLeft: 0, // We'll handle positioning with the animated position
                  zIndex: selectedRacquet === 'rac2' ? 2 : 1,
                },
              ]}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1440),
    margin: 'auto',
    flexDirection: 'row',
    alignItems: 'flex-start',
    position: 'relative',
    height: '100%',
  },
  leftSection: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    position: 'relative',
    top: scale(150),
    minHeight: scale(400),
    paddingTop: scale(20),
    paddingBottom: scale(20),
  },
  rightSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    position: 'relative',
    top: scale(130),
    right: 0,
    height: 'auto',
  },
  rightSectionSelected: {
    top: scale(200),
  },

  timeSelectors: {
    gap: 25,
    marginVertical: 40,
    minHeight: scale(150),
    width: '100%',
  },
  buttonContainer: {
    gap: 30,
  },
  actionButton: {
    alignItems: 'flex-start',
    width: scale(594),
  },
  rac1: {
    width: scale(273),
    height: verticalScale(913),
  },
  rac2: {
    width: scale(281),
    height: verticalScale(943),
  },
  leftButton: {
    width: scale(60),
    height: scale(60),
  },
  label: {
    fontSize: scale(53),
    textAlign: 'center',
    marginHorizontal: 10,
    lineHeight: verticalScale(48),
  },
  instructionText: {
    fontSize: moderateScale(24),
    minHeight: verticalScale(100),
  },
});

export default ModifyReservation;
