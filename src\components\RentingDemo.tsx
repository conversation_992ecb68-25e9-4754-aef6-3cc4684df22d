import React, {useState} from 'react';
import CustomSearchInput from './CustomSearchInput';
import {
  ImageRequireSource,
  ScrollView,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';
import KioskConfig from '../config/KioskConfig';
import CustomButton from './CustomButton';
import Typography from './Typography';
import {scale, verticalScale} from '@/utils/scale';
import FastImage, {ImageStyle} from 'react-native-fast-image';
import {useNavigation} from '@react-navigation/native';
import {useLanguageStore} from '@/store/languageStore';

interface RentingDemoProps {
  containerStyle?: ViewStyle;
  showSearch?: boolean;
  actionButtons: ActionButton[];
  btnStyle?: ViewStyle;
  btnContainerStyle?: ViewStyle;
  imgContainerStyle?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  activeImage?: ImageRequireSource;
  title?: string;
  imgStyle?: ImageStyle;
  // Optional handler for navigation - if not provided, will use internal navigation
  onNavigate?: (screenName: string, params?: Record<string, any>) => void;
}

interface ActionButton {
  id: number;
  title: string;
  onPress?: () => void;
  screenName?: string;
  params?: Record<string, any>;
  icon?: string;
  size: number;
}

const RentingDemo = (props: RentingDemoProps) => {
  const {
    containerStyle,
    showSearch = true,
    actionButtons = [],
    btnStyle,
    btnContainerStyle,
    imgContainerStyle,
    contentContainerStyle,
    activeImage,
    title,
    imgStyle,
    onNavigate,
  } = props;

  const navigation = useNavigation();
  const [expanded, setExpanded] = useState(false);
  const {t} = useLanguageStore();

  // Create a single navigation handler outside the map function
  const handleNavigation = (
    screenName?: string,
    params?: Record<string, any>,
    title: string,
  ) => {
    if (!screenName) return;

    // Use the provided navigation handler if available, otherwise use internal navigation
    if (onNavigate) {
      onNavigate(screenName, params, title);
    } else {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      navigation.navigate(screenName as any, params as any);
    }
  };

  return (
    <View
      style={[
        styles.container,
        {
          opacity: expanded ? 0 : 1,
        },
        containerStyle,
      ]}>
      <View style={[styles.contentContainer, contentContainerStyle]}>
        <Typography
          variant="faqAnswer"
          style={showSearch ? styles.title : styles.searchTitle}
          color={KioskConfig.theme.colors.white}>
          {title}
        </Typography>
        {showSearch && (
          <CustomSearchInput
            btn={styles.customBtnInput}
            setExpandedOption={setExpanded}
          />
        )}
        <View style={[styles.buttonContainer, btnContainerStyle]}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollView}>
            {actionButtons.map(data => {
              // Create onPress handler based on screenName or use existing onPress
              const handlePress = data.screenName
                ? () =>
                    handleNavigation(data.screenName, data.params, data.title)
                : data.onPress;

              return (
                <CustomButton
                  text={
                    data.title.startsWith('rentDemoBuy.') ||
                    data.title.startsWith('selectSport.')
                      ? t(data.title)
                      : data.title
                  }
                  onPress={handlePress}
                  key={data.id}
                  style={btnStyle}
                  icon={data.icon}
                  size={scale(data.size)}
                />
              );
            })}
          </ScrollView>
        </View>
      </View>
      <View style={[styles.imageContainer, imgContainerStyle]}>
        <FastImage
          source={activeImage}
          style={[styles.image, imgStyle]}
          resizeMode="contain"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    maxWidth: scale(1440),
    margin: 'auto',
    alignItems: 'center',
    flexDirection: 'row',
    position: 'relative',
  },
  contentContainer: {
    flex: 1,
  },
  scrollView: {
    gap: 26,
  },
  title: {
    marginBottom: 36,
  },
  searchTitle: {
    marginBottom: 0,
  },
  buttonContainer: {
    display: 'flex',
    gap: 30,
    marginTop: scale(24),
  },
  image: {
    width: scale(534),
    height: verticalScale(1089),
  },
  customBtnInput: {
    width: scale(596),
    height: verticalScale(104),
  },
  imageContainer: {
    position: 'absolute',
    right: 0,
    flex: 1,
  },
});

export default RentingDemo;
