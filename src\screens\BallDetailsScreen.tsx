import React, {useEffect, useMemo} from 'react';
import GradientBackground from '../components/GradientBackground';
import {Dimensions, StyleSheet, TouchableOpacity, View} from 'react-native';
import BrandedBallsCategory from '@/components/BrandedBallsCatogory';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import ReanimatedCarousel from '@/components/ReanimatedCarousel';
import AnimatedProductDetails from '@/components/Dunlop/AnimatedProductDetails';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import {BallsData} from '@/utils/staticData';
import {scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';

interface ActionButton {
  id: number;
  title: string;
  onPress: () => void;
}

interface CarouselItem {
  id: string;
  title: string;
  description: string;
  image: number;
}

interface CarouselBallItem extends CarouselItem {
  ballImage: number;
}

const BallDetailsScreen = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const route = useRoute<RouteProp<MainStackParamList, 'BallDetails'>>();
  const {ball, brand} = route.params;
  const {t} = useLanguageStore();
  // Find the initial index based on the ball ID
  const initialIndex = useMemo(() => {
    return BallsData.findIndex(item => item.id === ball.id);
  }, [ball.id]);

  // Single animation value for the screen
  const screenOpacity = useSharedValue(0);

  // Initialize animation
  useEffect(() => {
    // Simple fade-in animation
    screenOpacity.value = withTiming(1, {
      duration: 600,
      easing: Easing.out(Easing.ease),
    });
  }, []);

  // Create animated style
  const fadeStyle = useAnimatedStyle(() => {
    return {
      opacity: screenOpacity.value,
    };
  });

  const actionButtons: ActionButton[] = [
    {
      id: 1,
      title: t('productOptionSelected.checkout'),
      onPress: () => {
        navigation.navigate('Timer');
      },
    },
    {
      id: 2,
      title: t('productOptionSelected.continueShopping'),
      onPress: () => {
        navigation.navigate('RentDemoBuy');
      },
    },
  ];

  const radioOptions = [
    {label: 'ATP Extra Duty', value: 'atp_extra_duty'},
    {label: 'ATP Regular Duty', value: 'atp_regular_duty'},
  ];

  const renderBallDetails = (item: CarouselBallItem, _index: number) => {
    return (
      <TouchableOpacity activeOpacity={1} style={styles.itemContainer}>
        <View style={[styles.contentContainer]}>
          <AnimatedProductDetails
            title={t(item.title)}
            description={t(item.description)}
            actionButtons={actionButtons}
            radioOptions={radioOptions}
            radioTitle="Select one"
            showCounter={true}
            counterTitle="Quantity"
            btnStyle={styles.btnStyle}
            brand={brand}
          />
        </View>
        <Animated.View style={fadeStyle}>
          <BrandedBallsCategory
            image={item.image}
            ballImage={item.ballImage}
            showAddIcon={false}
            style={styles.image}
            ballImageStyle={styles.ballImage}
            ballImageContainerStyle={styles.ballImageContainer}
          />
        </Animated.View>
      </TouchableOpacity>
    );
  };

  const carouselData = BallsData.map(ball => ({
    id: String(ball.id),
    title: ball.title,
    description: ball.description,
    image: ball.image,
    ballImage: ball.ballImage,
  })) as unknown as CarouselItem[];

  const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

  return (
    <GradientBackground>
      <Animated.View style={[styles.mainContainer, fadeStyle]}>
        <View style={styles.container}>
          <ReanimatedCarousel
            data={carouselData}
            width={screenWidth * 0.8}
            height={screenHeight * 1}
            autoPlay={false}
            autoPlayInterval={5000}
            showArrows={true}
            showPagination={false}
            loop={false}
            style={styles.carousel}
            customRenderItem={(item, index) =>
              renderBallDetails(item as CarouselBallItem, index)
            }
            onSnapToItem={index => console.log('Current index:', index)}
            initialIndex={initialIndex}
          />
        </View>
      </Animated.View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
  },
  contentContainer: {
    alignItems: 'flex-start',
    justifyContent: 'center',
    maxWidth: scale(489),
  },
  itemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxWidth: scale(1440),
    margin: 'auto',
    alignItems: 'center',
    position: 'relative',
    gap: scale(300),
  },
  image: {
    width: scale(298),
    height: verticalScale(831),
    marginRight: scale(180),
  },
  carousel: {
    margin: 40,
  },
  ballImage: {
    width: scale(283),
    height: verticalScale(280),
    right: scale(230),
  },
  ballImageContainer: {
    position: 'absolute',
    right: '-40%',
    bottom: '-7%',
  },
  btnStyle: {
    height: scale(86),
    width: scale(489),
  },
});
export default BallDetailsScreen;
