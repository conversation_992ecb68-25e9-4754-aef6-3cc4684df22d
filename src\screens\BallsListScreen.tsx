import React, {useRef, useState} from 'react';
import GradientBackground from '../components/GradientBackground';
import {
  StyleSheet,
  View,
  FlatList,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import BrandedBallsCategory from '@/components/BrandedBallsCatogory';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {scale, verticalScale} from '@/utils/scale';
import {BallsData} from '@/utils/staticData';
import Icon from '@/components/Icon';
import {getBrandName} from '@/utils/CommonFunctions';
import {useLanguageStore} from '@/store/languageStore';

type BallsListScreenRouteProp = RouteProp<MainStackParamList, 'BallsList'>;

type BallCategory = {
  id: number;
  image: number;
  title: string;
  bottomTitle: string;
  ballImage: number;
  description: string;
  radioOptions: {label: string; value: string}[];
};

const BallsListScreen = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const route = useRoute<BallsListScreenRouteProp>();
  const {brand} = route.params || {};
  const {t} = useLanguageStore();

  const screenWidth = Dimensions.get('window').width * 0.95;
  const ITEM_WIDTH = screenWidth / 4;
  const flatListRef = useRef<FlatList>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const contentWidth = event.nativeEvent.contentSize.width;
    const layoutWidth = event.nativeEvent.layoutMeasurement.width;

    setShowLeftArrow(contentOffsetX > 0);
    setShowRightArrow(contentOffsetX < contentWidth - layoutWidth - 1);

    // Update current index based on scroll position
    const newIndex = Math.round(contentOffsetX / ITEM_WIDTH);
    setCurrentIndex(newIndex);
  };

  const scrollToNext = () => {
    if (flatListRef.current) {
      const nextIndex = Math.min(currentIndex + 3, BallsData.length - 1);
      flatListRef.current.scrollToIndex({
        index: nextIndex,
        animated: true,
        viewPosition: 0,
      });
    }
  };

  const scrollToPrevious = () => {
    if (flatListRef.current) {
      const prevIndex = Math.max(currentIndex - 3, 0);
      flatListRef.current.scrollToIndex({
        index: prevIndex,
        animated: true,
        viewPosition: 0,
      });
    }
  };

  const renderItem = ({item: category}: {item: BallCategory}) => (
    <TouchableOpacity
      activeOpacity={1}
      style={{width: ITEM_WIDTH, alignItems: 'center'}}>
      <BrandedBallsCategory
        key={category.id}
        image={category.image}
        ballImage={category.ballImage}
        style={styles.image}
        title={t(category.bottomTitle)}
        addOnPress={() => {
          navigation.navigate('BallDetails', {
            ball: category,
            brand: brand,
          });
        }}
      />
    </TouchableOpacity>
  );

  return (
    <GradientBackground>
      <View style={styles.brandContainer}>
        <Icon
          name={getBrandName(brand)}
          size={brand === 'head' ? scale(50) : scale(80)}
          color="white"
        />
      </View>
      {showLeftArrow && (
        <TouchableOpacity
          style={[styles.arrowButton, styles.leftArrow]}
          onPress={scrollToPrevious}>
          <Icon name="Left-chevron" size={50} color="#fff" />
        </TouchableOpacity>
      )}
      <FlatList
        ref={flatListRef}
        data={BallsData}
        renderItem={renderItem}
        horizontal
        keyExtractor={(item: BallCategory) => item.id.toString()}
        showsHorizontalScrollIndicator={false}
        snapToInterval={ITEM_WIDTH}
        decelerationRate="fast"
        onScroll={handleScroll}
        scrollEventThrottle={16}
        contentContainerStyle={styles.flatListContainer}
        getItemLayout={(data, index) => ({
          length: ITEM_WIDTH,
          offset: ITEM_WIDTH * index,
          index,
        })}
      />
      {showRightArrow && (
        <TouchableOpacity
          style={[styles.arrowButton, styles.rightArrow]}
          onPress={scrollToNext}>
          <Icon name="Right-chevron" size={50} color="#fff" />
        </TouchableOpacity>
      )}
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  flatListContainer: {
    flexGrow: 1,
    paddingTop: verticalScale(20),
    justifyContent: 'center',
    marginTop: verticalScale(80),
  },
  arrowButton: {
    position: 'absolute',
    top: '50%',
    transform: [{translateY: -20}],
    borderRadius: 25,
    padding: 10,
    zIndex: 1,
  },
  leftArrow: {
    left: 10,
  },
  rightArrow: {
    right: 10,
  },
  image: {
    width: scale(141.61),
    height: verticalScale(394.49),
  },
  brandImage: {
    width: scale(470.05),
    height: verticalScale(72.43),
  },
  brandContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: verticalScale(100),
  },
  ballContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
});
export default BallsListScreen;
