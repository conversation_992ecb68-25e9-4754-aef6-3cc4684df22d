import images from '@/config/images';
import {ImageSourcePropType, TextStyle} from 'react-native';

interface NavigationButton {
  id: number;
  title: string;
  screenName: string | null; // Changed to string to match ActionButton
  params?: Record<string, unknown>; // More specific than any
  icon?: string;
  size: number;
  iconStyle?: TextStyle;
}

interface ActionButton {
  id: number;
  title?: string;
  image?: ImageSourcePropType;
  brand?: string;
}

export const actionButtons: NavigationButton[] = [
  {
    id: 1,
    title: 'rentDemoBuy.racquets',
    screenName: 'SelectSport',
    params: {from: 'racquet'},
    icon: 'rac-2',
    size: 50,
  },
  {
    id: 2,
    title: 'rentDemoBuy.balls',
    screenName: 'SelectSport',
    params: {from: 'ball'},
    icon: 'ball',
    size: 40,
  },
  {
    id: 3,
    title: 'rentDemoBuy.gear',
    screenName: null,
    icon: 'bag',
    size: 65,
  },
  {
    id: 4,
    title: 'rentDemoBuy.locker',
    screenName: null,
    icon: 'locker',
    size: 70,
  },
  {
    id: 5,
    title: 'rentDemoBuy.courts',
    screenName: null,
    icon: 'Courts',
    size: 40,
  },
];

export const selectSportActionButtons: NavigationButton[] = [
  {
    id: 1,
    title: 'selectSport.tennis',
    screenName: 'SelectBrand',
    // params: {from},
    icon: 'Tennis',
    size: 40,
  },
  {
    id: 2,
    title: 'selectSport.padel',
    screenName: 'SelectBrand',
    icon: 'padel',
    size: 40,
  },
  {
    id: 3,
    title: 'selectSport.pickleball',
    screenName: 'SelectBrand',
    // params: {from},
    icon: 'pickleball',
    size: 40,
  },
  {
    id: 4,
    title: 'selectSport.platformTennis',
    screenName: 'SelectBrand',
    // params: {from},
    icon: 'platform-tennis',
    size: 40,
  },
];

export const selectBrandRacquetActions: ActionButton[] = [
  {
    id: 1,
    image: images.dunlop,
    brand: 'dunlop',
  },
  {
    id: 2,
    image: images.babolat,
    brand: 'babolat',
  },
  {
    id: 3,
    image: images.yonex,
    brand: 'yonex',
  },
  {
    id: 4,
    image: images.head,
    brand: 'head',
  },
  {
    id: 5,
    image: images.technifibre,
    brand: 'technifibre',
  },
];

export const selectBrandBallActions = {
  image: images.balls,
  actions: [
    {
      id: 1,
      screenName: 'BallsList',
      brand: 'dunlop',
    },
    {
      id: 2,
      screenName: 'BallsList',
      brand: 'wilson',
    },
    {
      id: 3,
      screenName: 'BallsList',
      brand: 'penn',
    },
    {
      id: 4,
      screenName: 'BallsList',
      brand: 'yonex',
    },
    {
      id: 5,
      screenName: 'BallsList',
      brand: 'head',
    },
  ],
};

export const selectBrandJson = {
  title: 'selectBrand.title',
  buttons: [
    {
      id: 1,
      text: 'selectBrand.brand',
      screenName: 'SelectBrandsBall',
      altScreenName: 'SelectBrandsRacquet',
    },
    {
      id: 2,
      text: 'selectBrand.helpMeChoose',
      altText: 'selectBrand.racquetGuide',
      screenName: 'HelpMeChoose',
      altScreenName: 'ExperienceGuide',
    },
  ],
};

export const BallsData = [
  {
    id: 1,
    image: images.AtpTour,
    title: 'ballsList.atpTour.title',
    bottomTitle: 'ballsList.atpTour.bottomTitle',
    ballImage: images.atp1,
    description: 'ballsList.atpTour.description',
    radioOptions: [
      {label: 'ballsList.atpTour.radioOptions_1', value: 'atp_extra_duty'},
      {label: 'ballsList.atpTour.radioOptions_2', value: 'atp_regular_duty'},
    ],
  },
  {
    id: 2,
    image: images.GrandPrix,
    title: 'ballsList.grandPrix.title',
    bottomTitle: 'ballsList.grandPrix.bottomTitle',
    ballImage: images.grandPrix1,
    description: 'ballsList.grandPrix.description',
    radioOptions: [
      {label: 'ballsList.grandPrix.radioOptions_1', value: 'atp_extra_duty'},
      {label: 'ballsList.grandPrix.radioOptions_2', value: 'atp_regular_duty'},
    ],
  },
  {
    id: 3,
    image: images.AtpChamp,
    title: 'ballsList.atpChamp.title',
    bottomTitle: 'ballsList.atpChamp.bottomTitle',
    ballImage: images.dunlop1,
    description: 'ballsList.atpChamp.description',
    radioOptions: [
      {label: 'ballsList.atpChamp.radioOptions_1', value: 'atp_extra_duty'},
      {label: 'ballsList.atpChamp.radioOptions_2', value: 'atp_regular_duty'},
    ],
  },
  {
    id: 4,
    image: images.aoBox,
    title: 'ballsList.atp.title',
    bottomTitle: 'ballsList.atp.bottomTitle',
    ballImage: images.NewAo,
    description: 'ballsList.atp.description',
    radioOptions: [
      {label: 'ballsList.atp.radioOptions_1', value: 'atp_extra_duty'},
      {label: 'ballsList.atp.radioOptions_2', value: 'atp_regular_duty'},
    ],
  },
  {
    id: 5,
    image: images.Stage1,
    title: 'ballsList.stage1.title',
    bottomTitle: 'ballsList.stage1.bottomTitle',
    ballImage: images.stage1Ball,
    description: 'ballsList.stage1.description',
  },
  {
    id: 6,
    image: images.Stage2,
    title: 'ballsList.stage2.title',
    bottomTitle: 'ballsList.stage2.bottomTitle',
    ballImage: images.stage2Ball,
    description: 'ballsList.stage2.description',
  },
];

export const ballDetailButtonConfigs = [
  {
    text: `ballDetailsActionButtons.bestBallForThisCourtLocation`,
    screenName: 'BallDetailsWithoutCarousel',
  },
  {
    text: 'ballDetailsActionButtons.ballByType',
    screenName: 'SelectBallByType',
  },
  {
    text: 'ballDetailsActionButtons.takeADeeperDive',
    screenName: 'EquipMentType',
  },
];

export const ballTypeButtonConfigs = {
  title: 'ballByType.title',
  buttonsConfigs: [
    {
      id: 1,
      title: 'ballByType.btnText1',
      screenName: 'BallDetailsWithoutCarousel',
      params: {
        type: 'ball_by_type',
      },
    },
    {
      id: 2,
      title: 'ballByType.btnText2',
      screenName: 'BallDetailsWithoutCarousel',
      params: {
        type: 'ball_by_type',
      },
    },
    {
      id: 3,
      title: 'ballByType.btnText3',
      screenName: 'BallDetailsWithoutCarousel',
      params: {
        type: 'ball_by_type',
      },
    },
    {
      id: 4,
      title: 'ballByType.btnText4',
      screenName: 'BallDetailsWithoutCarousel',
      params: {
        type: 'ball_by_type',
      },
    },
    {
      id: 5,
      title: 'ballByType.btnText5',
      screenName: 'BallDetailsWithoutCarousel',
      params: {
        type: 'ball_by_type',
      },
    },
  ],
};

export const PadelData = [
  {
    id: 1,
    image: images.galacticaPro,
    title: 'padelList.galacticaPro.title',
    type: 'padelList.galacticaPro.type',
    description: 'padelList.galacticaPro.description',
    colors: [
      {
        value: '#24B4B4',
        label: '#24B4B4',
      },
    ],
    size: {width: 340, height: 567},
  },
  {
    id: 2,
    image: images.aeroStarPro,
    title: 'padelList.aeroStarPro.title',
    type: 'padelList.aeroStarPro.type',
    description: 'padelList.aeroStarPro.description',
    colors: [
      {
        value: '#B4925D',
        label: '#B4925D',
      },
    ],
    size: {width: 366, height: 585},
  },
  {
    id: 3,
    image: images.galacticaProLs,
    title: 'padelList.galacticaProLs.title',
    type: 'padelList.galacticaProLs.type',
    description: 'padelList.galacticaProLs.description',
    colors: [
      {
        value: '#81BD7C',
        label: '#81BD7C',
      },
    ],
    size: {width: 340, height: 567},
  },
  {
    id: 4,
    image: images.aeroStarPro,
    title: 'padelList.aeroStarPro.title',
    type: 'padelList.aeroStarPro.type',
    description: 'padelList.aeroStarPro.description',
    colors: [
      {
        value: '#B4925D',
        label: '#B4925D',
      },
    ],
    size: {width: 366, height: 585},
  },
];

export const RacquetsData = [
  {
    id: 1,
    image: images.dunlopCx200,
    title: 'racquetList.cx200',
    type: 'racquetList.control',
    description: 'racquetList.description',
    colors: [
      {
        value: '#FD725D',
        label: '#FD725D',
      },
    ],
  },
  {
    id: 2,
    image: images.dunlopSx300,
    title: 'racquetList.sx300',
    type: 'racquetList.spin',
    description: 'racquetList.description',
    colors: [
      {
        value: '#CEE936',
        label: '#CEE936',
      },
    ],
  },
  {
    id: 3,
    image: images.dunlopFx500,
    title: 'racquetList.fx500',
    type: 'racquetList.power',
    description: 'racquetList.description',
    colors: [
      {
        value: '#0050A4',
        label: '#0050A4',
      },
    ],
  },
  {
    id: 4,
    image: images.dunlopLX800,
    title: 'racquetList.fx500',
    type: 'racquetList.power',
    description: 'racquetList.description',
    colors: [
      {
        value: '#989899',
        label: '#989899',
      },
    ],
  },
];
