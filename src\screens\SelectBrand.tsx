import React, {useCallback} from 'react';
import GradientBackground from '../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import CustomButton from '../components/CustomButton';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {useNavigation, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';
import {selectBrandJson} from '@/utils/staticData';

export type SelectBrandScreenProps = {
  route: RouteProp<MainStackParamList, 'SelectBrand'>;
};

const SelectBrandScreen = ({route}: SelectBrandScreenProps) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {from = 'ball'} = route.params || {};
  const {t} = useLanguageStore();
  // Create a single navigation handler for all buttons
  const handleNavigation = useCallback(
    (
      screenName: keyof MainStackParamList,
      params?: Record<string, unknown>,
    ) => {
      navigation.navigate(screenName as any, params);
    },
    [navigation],
  );

  const handleButtonPress = useCallback(
    (button: (typeof selectBrandJson)['buttons'][0]) => {
      const screenName =
        from === 'ball' ? button.screenName : button.altScreenName;
      handleNavigation(screenName as keyof MainStackParamList, {from});
    },
    [from, handleNavigation],
  );

  return (
    <GradientBackground>
      <View style={styles.container}>
        <Typography variant="faqAnswer" color={KioskConfig.theme.colors.white}>
          {t(selectBrandJson?.title)}
        </Typography>
        <View style={styles.buttonContainer}>
          {selectBrandJson?.buttons?.map((button, index) => (
            <CustomButton
              key={index}
              text={t(
                button.altText && from !== 'ball'
                  ? button.altText
                  : button.text,
              )}
              style={styles.button}
              onPress={() => handleButtonPress(button)}
            />
          ))}
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: scale(100),
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 75,
    marginTop: 40,
  },
  titleContainer: {
    alignSelf: 'flex-start',
  },
  button: {
    minWidth: scale(520),
    height: verticalScale(172),
  },
});
export default SelectBrandScreen;
