import React, {useState} from 'react';
import {View, StyleSheet} from 'react-native';
import CustomCalendar from '../components/CustomCalendar';
import ProductDetails from '@/components/Dunlop/ProductDetails';
import GradientBackground from '@/components/GradientBackground';
import images from '@/config/images';
import FastImage from 'react-native-fast-image';

interface ProductDetailScreenProps {
  route?: {
    params?: {
      productId?: string;
    };
  };
}

const ProductDetailScreen: React.FC<ProductDetailScreenProps> = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());

  const handleReserve = () => {
    console.log('Reserved for', selectedDate);
  };

  const handlePurchase = () => {
    console.log('Purchased');
  };

  const getCalendar = () => (
    <View style={styles.calendarWrapper}>
      <CustomCalendar
        selectedDate={selectedDate}
        onSelectDate={setSelectedDate}
        minDate={new Date()} // Disables dates before today
      />
    </View>
  );

  return (
    <GradientBackground backButton={false}>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <ProductDetails
            title="FX 500"
            description="Take on all comers with the maneuverable and powerful FX 500. An efficient yet plush feel, built to help you rip the ball with confidence and precision. An absolute game changer."
            btnStyle={styles.actionButton}
            textStyle={styles.btnStyle}
            actionButtons={[
              {
                id: 1,
                title: 'Reserve',
                onPress: handleReserve,
                highlighted: false,
              },
              {
                id: 2,
                title: 'Purchase',
                onPress: handlePurchase,
                highlighted: false,
              },
            ]}
            calendar={() => getCalendar()}
            colors={[
              {
                value: 'blue',
                label: 'Blue',
              },
              {
                value: 'red',
                label: 'Red',
              },
            ]}
          />
        </View>
        <View>
          <FastImage
            source={images.racquet}
            style={styles.image}
            resizeMode="contain"
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignSelf: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contentContainer: {
    width: '50%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  raqtView: {
    flex: 1,
  },
  image: {
    width: 534,
    height: 1089,
  },
  calendarWrapper: {
    // marginBottom: 15,
  },
  actionsContainer: {
    marginTop: 15,
  },
  btnStyle: {
    fontSize: 25,
    lineHeight: 30,
  },
  actionButton: {
    marginBottom: 10,
    display: 'flex',
    width: 309,
    height: 54,
    paddingVertical: 0,
    flexDirection: 'column',
    justifyContent: 'center',
    flexShrink: 0,
  },
  footer: {
    height: 50,
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingHorizontal: 20,
  },
  footerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerText: {
    color: 'white',
    fontSize: 12,
    marginRight: 10,
  },
});

export default ProductDetailScreen;
