/**
 * Font sizes for the Kiosk app
 */
import {moderateScale} from './scale';

// Original font sizes (for reference)
const ORIGINAL_FONT_SIZE = {
  xs: 10,
  sm: 12,
  md: 14,
  lg: 16,
  lg2: 16.99,
  xl: 18,
  xl2: 19.2,
  xl3: 19.84,
  xxl: 20,
  xxxl: 22,
  subTitle: 24,
  title: 28,
  heading: 32,
  heading0: 32.46,
  heading1: 40,
  heading2: 48,
  userTitle: 96,
  customButton: 48,
  timer: 132.98,
  congrats: 97.5,
  f45: 45,
  f30: 30,
  xxxxl: 28,
  tab: 11.34, // Added missing size used in dunlopTitle
  f25: 25.61,
  f28: 28.45,
};

// Scaled font sizes
export const FONT_SIZE = Object.entries(ORIGINAL_FONT_SIZE).reduce(
  (acc, [key, value]) => ({
    ...acc,
    [key]: moderateScale(value),
  }),
  {} as typeof ORIGINAL_FONT_SIZE,
);
