import React from 'react';
import {View, StyleSheet} from 'react-native';
import GradientBackground from '../../components/GradientBackground';
import KioskConfig from '../../config/KioskConfig';
import Typography from '../../components/Typography';
import CustomButton from '../../components/CustomButton';
import {useReservation} from '../../context/ReservationContext';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '../../navigation';
import {scale, verticalScale} from '@/utils/scale';
import {dynamicResetStack} from '@/utils/CommonFunctions';
import {useLanguageStore} from '@/store/languageStore';

export type CheckOutOrReturnScreenProps = {
  route: RouteProp<MainStackParamList, 'CheckOutOrReturnScreen'>;
};

const CheckOutOrReturnScreen = ({route}: CheckOutOrReturnScreenProps) => {
  const {flowType} = useReservation();
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  const {
    from = '',
    item = null,
    selectedOption = '',
    type = '',
  } = route?.params || {};
  const arr1 = [
    {
      id: 1,
      title: t('cartWindow.confirm'),
      onPress: () => navigation.navigate('SomethingElse'),
    },
    {
      id: 2,
      title: t('cartWindow.modifyReservation'),
      onPress: () => navigation.navigate('ModifyReservation'),
    },
    {
      id: 3,
      title: t('cartWindow.addToOrder'),
      // onPress: () => navigation.navigate('AddToOrderScreen'),
    },
  ];

  const arr2 = [
    {
      id: 1,
      title: t('cartWindow.returnEquipment'),
      onPress: () => navigation.navigate('ReturnScreen'),
    },
    {
      id: 1,
      title: t('cartWindow.extendPlaytime'),
      onPress: () => navigation.navigate('ModifyReservation'),
    },
  ];
  const handleCartPress = () => {
    console.log('Cart pressed in CheckOutOrReturnScreen');
  };

  const checkOutArr = [
    {
      id: 1,
      title: t('cartWindow.confirm'),
      onPress: () => navigation.navigate('Dispensing', {from: from}),
    },
    {
      id: 2,
      title: t('cartWindow.modifyReservation'),
      onPress: () => {
        dynamicResetStack(navigation, 9, 'start');
        // navigation.navigate('ModifyReservationScreen');
      },
    },
    {
      id: 3,
      title: t('cartWindow.continueShopping'),
      onPress: () => {
        dynamicResetStack(navigation, 7, 'start');
        // navigation.replace('ExperienceGuide', {from: from});
      },
    },
  ];
  const title =
    from || flowType === 'CHECKOUT'
      ? t('cartWindow.letReviewOrder')
      : t('cartWindow.welcomeBack');
  const buttons = from ? checkOutArr : flowType === 'CHECKOUT' ? arr1 : arr2;

  return (
    <GradientBackground cart={true} onCartPress={handleCartPress}>
      <View style={styles.container}>
        <View style={styles.cartWindow}>
          <Typography variant="heading1" color={KioskConfig.theme.colors.black}>
            {t('cartWindow.title')}
          </Typography>
          <Typography
            color={KioskConfig.theme.colors.black}
            variant="subtitle2">
            {t('cartWindow.description')}
          </Typography>
        </View>

        <View style={styles.buttonContainer}>
          <Typography variant="heading2" color={KioskConfig.theme.colors.white}>
            {title}
          </Typography>
          {buttons.map(button => (
            <CustomButton
              style={styles.button}
              key={button.id}
              text={button.title}
              onPress={button.onPress}
              textStyle={styles.buttonText}
            />
          ))}
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    maxWidth: scale(1440),
    gap: scale(220),
    margin: 'auto',
    paddingBottom: scale(100),
  },
  cartWindow: {
    width: scale(640),
    height: verticalScale(823),
    backgroundColor: KioskConfig.theme.colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 30,
  },
  button: {
    width: scale(594),
    height: verticalScale(104),
  },
  buttonText: {
    color: KioskConfig.theme.colors.black,
  },
});

export default CheckOutOrReturnScreen;
