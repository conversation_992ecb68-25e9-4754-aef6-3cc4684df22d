import React, {useState, useCallback} from 'react';
import GradientBackground from '../components/GradientBackground';
import RentingDemo from '../components/RentingDemo';
import {StyleSheet, ImageRequireSource, View} from 'react-native';
import images from '@config/images';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {scale, verticalScale} from '@/utils/scale';
import {selectSportActionButtons} from '@/utils/staticData';
import {useLanguageStore} from '@/store/languageStore';

// Simplified interface with just screen name and params
interface NavigationButton {
  id?: number;
  title?: string;
  screenName?: keyof MainStackParamList;
  params?: Record<string, unknown>;
  icon?: string;
  size?: number;
}

interface RouteParams {
  from?: string;
}

const SelectSport = () => {
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const {from} = route.params || {};
  const {t} = useLanguageStore();

  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();

  // Create a single navigation handler for all buttons
  const handleNavigation = useCallback(
    (screenName: string, params?: Record<string, unknown>, title?: string) => {
      if (params?.from === 'racquet' && title === 'selectSport.padel') {
        navigation.navigate('SelectBrandsPadel', params);
      } else {
        navigation.navigate(screenName as any, params);
      }
    },
    [navigation],
  );

  const [activeImage] = useState<ImageRequireSource>(
    from === 'ball' ? images.balls : images.racquet,
  );

  let actionData: NavigationButton = selectSportActionButtons;
  if (from) {
    actionData = selectSportActionButtons.map((d: NavigationButton) => ({
      ...d,
      params: {from},
    }));
  }
  console.log('selectSportActionButtons ===>', actionData);

  return (
    <GradientBackground>
      <RentingDemo
        containerStyle={styles.container}
        actionButtons={actionData as any} // Type assertion to fix compatibility
        btnStyle={styles.btnStyle}
        activeImage={activeImage}
        title={t('selectSport.title')}
        showSearch={false}
        btnContainerStyle={styles.btnContainerStyle}
        imgStyle={from === 'ball' ? styles.imgStyle : {}}
        imgContainerStyle={from === 'ball' ? {} : {top: -20, right: scale(100)}}
        onNavigate={handleNavigation} // Pass the navigation handler
      />
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 'auto',
  },
  btnStyle: {
    width: scale(594),
    height: verticalScale(104),
    // paddingHorizontal: scale(24),
    // justifyContent: 'center',
    // alignItems: 'flex-start',
  },
  btnContainerStyle: {
    gap: 26,
  },
  imgStyle: {
    width: scale(712),
    height: verticalScale(748),
  },
});

export default SelectSport;
