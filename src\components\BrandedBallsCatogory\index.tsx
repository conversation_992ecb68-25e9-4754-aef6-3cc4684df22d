import React, {memo, useState} from 'react';
import {
  ImageRequireSource,
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import images from '@/config/images';
import Typography from '../Typography';
import KioskConfig from '@/config/KioskConfig';
import Icon from '../Icon';
import {scale, verticalScale} from '@/utils/scale';
import FastImage, {ImageStyle} from 'react-native-fast-image';
import LottieView from 'lottie-react-native';
import animation from '@/config/animation';

const BrandedBallsCategory = ({
  image,
  ballImage,
  style = {},
  ballImageStyle = {},
  title,
  addOnPress,
  showAddIcon = true,
  ballImageContainerStyle,
}: {
  image: ImageRequireSource;
  ballImage: ImageRequireSource;
  style?: StyleProp<ImageStyle>;
  ballImageStyle?: StyleProp<ImageStyle>;
  title?: string;
  addOnPress?: () => void;
  showAddIcon?: boolean;
  ballImageContainerStyle?: StyleProp<ViewStyle>;
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  const handlePress = () => {
    if (!isAnimating) {
      setIsAnimating(true);
    }
  };

  const handleAnimationFinish = () => {
    setIsAnimating(false);
    if (addOnPress) {
      addOnPress();
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      style={styles.root}
      onPress={handlePress}>
      <FastImage
        source={image || images.dunlopBalls_ATPTour}
        style={[styles.image, style as ImageStyle]}
      />
      <View style={[styles.ballImageContainer, ballImageContainerStyle]}>
        <FastImage
          source={ballImage || images.ball}
          style={[styles.ballImage, ballImageStyle as ImageStyle]}
        />
      </View>

      {showAddIcon && (
        <View style={styles.addIcon}>
          {/* <Icon
            name="add"
            size={scale(78)}
            color={KioskConfig.theme.colors.Mindaro}
          /> */}
          <View style={styles.addIconContainer}>
            <LottieView
              source={animation.plusIconAnimation}
              autoPlay={isAnimating}
              loop={false}
              onAnimationFinish={handleAnimationFinish}
              style={styles.plusBtn}
            />
          </View>
          <Typography variant="tryText" color={KioskConfig.theme.colors.white}>
            BUY
          </Typography>
        </View>
      )}

      {title && (
        <Typography
          variant="ballTitle"
          color={KioskConfig.theme.colors.white}
          numberOfLines={2}
          style={styles.title}>
          {title}
        </Typography>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  root: {
    minWidth: scale(300),
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: scale(141.61),
    height: verticalScale(394.48),
  },
  ballImageContainer: {
    position: 'absolute',
    right: '0%',
    bottom: '-10%',
  },
  ballImage: {
    width: scale(134.39),
    height: scale(132.94),
  },
  title: {
    position: 'absolute',
    bottom: verticalScale(-120),
    left: 0,
    right: 0,
    textAlign: 'center',
    minHeight: verticalScale(70),
  },
  addIcon: {
    alignItems: 'center',
    position: 'absolute',
    top: verticalScale(-50),
    right: scale(-30),
    zIndex: 1000,
  },
  addIconContainer: {
    width: scale(100),
    height: verticalScale(100),
    justifyContent: 'center',
    alignItems: 'center',
  },
  plusBtn: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default memo(BrandedBallsCategory);
