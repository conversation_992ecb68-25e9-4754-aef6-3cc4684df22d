/**
 * Scaling utilities for responsive UI
 *
 * These utilities help scale UI elements based on the device's screen size
 * relative to the design specifications (1920x1080).
 */
import { Dimensions } from 'react-native';
import KioskConfig from '../config/KioskConfig';

// Get the window dimensions
const { width, height } = Dimensions.get('window');

// Design specifications from Figma (using the kiosk target resolution)
const guidelineBaseWidth = KioskConfig.display.width; // 1920
const guidelineBaseHeight = KioskConfig.display.height; // 1080

/**
 * Scales a size horizontally based on the device's width
 * @param size The size to scale (in pixels)
 * @returns The scaled size
 */
export const scale = (size: number) => (width / guidelineBaseWidth) * size;

/**
 * Scales a size vertically based on the device's height
 * @param size The size to scale (in pixels)
 * @returns The scaled size
 */
export const verticalScale = (size: number) =>
  (height / guidelineBaseHeight) * size;

/**
 * Scales a size with a factor to moderate the scaling effect
 * @param size The size to scale (in pixels)
 * @param factor The factor to moderate scaling (default: 0.5)
 * @returns The moderately scaled size
 */
export const moderateScale = (size: number, factor = 0.5) =>
  size + (scale(size) - size) * factor;
