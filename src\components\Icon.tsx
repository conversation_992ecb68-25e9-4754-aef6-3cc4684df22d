import React from 'react';
import {Text, StyleSheet, TextStyle, TextProps} from 'react-native';
import selection from '@assets/fonts/icomoon/selection.json';
import {
  parseIconsFromSelection,
  getIcomoonFontFamily,
} from '../utils/iconUtils';
import {iconStyle} from '../utils/iconStyles';
import { moderateScale } from '@/utils/scale';

// Parse icons from selection.json
const IconMap = parseIconsFromSelection(selection);
// Get font family based on platform
const fontFamily = getIcomoonFontFamily(selection);

export type IconName = keyof typeof IconMap;

interface IconProps extends TextProps {
  name: IconName;
  size?: number;
  color?: string;
  style?: TextStyle;
}

const Icon: React.FC<IconProps> = ({
  name,
  size = moderateScale(24),
  color = '#000',
  style,
  ...props
}) => {
  return (
    <Text
      style={[
        styles.icon,
        {
          fontFamily,
          fontSize: size,
          color,
        },
        style,
      ]}
      {...props}>
      {IconMap[name]}
    </Text>
  );
};

const styles = StyleSheet.create({
  icon: {
    ...iconStyle,
  },
});

export default Icon;
