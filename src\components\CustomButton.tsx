import React, {useState, useRef} from 'react';
import {
  TouchableHighlight,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
  View,
  Modal,
  ScrollView,
  TouchableWithoutFeedback,
} from 'react-native';
import Typography from './Typography';
import Icon from './Icon';
import KioskConfig from '@/config/KioskConfig';
import {scale, verticalScale, moderateScale} from '@/utils/scale';
import {FONTS} from '@/utils/fonts';
import FastImage, {Source} from 'react-native-fast-image';
import animation from '@/config/animation';
import LottieView, {AnimationObject} from 'lottie-react-native';
import {getBrandName} from '@/utils/CommonFunctions';

interface CustomButtonProps {
  text: string;
  text2?: string;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  highlighted?: boolean;
  icon?: string;
  buttonContentContainerStyle?: StyleProp<ViewStyle>;
  image?: Source;
  size?: number;
  dropdown?: boolean;
  dropdownItems?: {label: string; value: string}[];
  onSelectItem?: (item: {label: string; value: string}) => void;
  showDefaultItems?: boolean;
  brand?: string;
}

const DEFAULT_DROPDOWN_ITEMS = [
  {label: 'Option 1', value: 'option1'},
  {label: 'Option 2', value: 'option2'},
  {label: 'Option 3', value: 'option3'},
  {label: 'Option 1', value: 'option1'},
  {label: 'Option 2', value: 'option2'},
  {label: 'Option 3', value: 'option3'},
  {label: 'Option 1', value: 'option1'},
  {label: 'Option 2', value: 'option2'},
  {label: 'Option 3', value: 'option3'},
  {label: 'Option 1', value: 'option1'},
  {label: 'Option 2', value: 'option2'},
  {label: 'Option 3', value: 'option3'},
  {label: 'Option 1', value: 'option1'},
  {label: 'Option 2', value: 'option2'},
  {label: 'Option 3', value: 'option3'},
  {label: 'Option 1', value: 'option1'},
  {label: 'Option 2', value: 'option2'},
  {label: 'Option 3', value: 'option3'},
  {label: 'Option 1', value: 'option1'},
  {label: 'Option 2', value: 'option2'},
  {label: 'Option 3', value: 'option3'},
];

// Animation dimensions map for reference
const buttonAnimationDimensions = [
  {key: 'buttonPill', width: 594, height: 104}, // AE-PillButton-594x104
  {key: 'buttonPill2', width: 520, height: 172}, // AE-PillButton-520x172
  {key: 'buttonPill3', width: 321, height: 54}, // PillButton-321x54
  {key: 'buttonPill4', width: 489, height: 86}, // PillButton-489x86
];

/**
 * Direct utility to get button animation by exact dimensions
 *
 * @param width - Width in non-scaled pixels
 * @param height - Height in non-scaled pixels
 * @returns The best matching animation
 */
export const getButtonAnimationByDimensions = (
  width: number,
  height: number,
): AnimationObject => {
  // Find the closest matching animation using Euclidean distance
  let bestMatch = buttonAnimationDimensions[0];
  let minDistance = Math.sqrt(
    Math.pow(width - buttonAnimationDimensions[0].width, 2) +
      Math.pow(height - buttonAnimationDimensions[0].height, 2),
  );

  for (let i = 1; i < buttonAnimationDimensions.length; i++) {
    const distance = Math.sqrt(
      Math.pow(width - buttonAnimationDimensions[i].width, 2) +
        Math.pow(height - buttonAnimationDimensions[i].height, 2),
    );

    if (distance < minDistance) {
      minDistance = distance;
      bestMatch = buttonAnimationDimensions[i];
    }
  }

  return animation[bestMatch.key as keyof typeof animation];
};

/**
 * Extracts width and height from style and returns the appropriate animation
 *
 * @param style - React Native style object that may contain width and height
 * @returns The best matching Lottie animation object
 */
export const getButtonAnimation = (
  style?: StyleProp<ViewStyle>,
): AnimationObject => {
  // Default dimensions from button style
  const defaultWidth = 596;
  const defaultHeight = 104;

  // Extract width and height from style prop with destructuring
  let styleWidth = defaultWidth;
  let styleHeight = defaultHeight;

  if (style) {
    const flattenedStyle = StyleSheet.flatten(style);
    const {width, height, maxWidth} = flattenedStyle;

    // Use width or maxWidth if available
    if (width !== undefined && typeof width === 'number') {
      styleWidth = width + scale(20);
    } else if (maxWidth !== undefined && typeof maxWidth === 'number') {
      styleWidth = maxWidth;
    }

    // Use height if available
    if (height !== undefined && typeof height === 'number') {
      styleHeight = height;
    }
  }

  // Use the dimensions to find the best animation
  return getButtonAnimationByDimensions(styleWidth, styleHeight);
};

const CustomButton: React.FC<CustomButtonProps> = ({
  text,
  text2,
  onPress,
  style,
  textStyle,
  highlighted = false,
  icon,
  buttonContentContainerStyle,
  image,
  size,
  dropdown = false,
  dropdownItems = [],
  onSelectItem,
  brand,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationRef = useRef<LottieView>(null);

  const itemsToShow = dropdownItems || DEFAULT_DROPDOWN_ITEMS;

  const handlePress = () => {
    if (dropdown) {
      setShowDropdown(true);
    } else if (onPress) {
      setIsAnimating(true);
      animationRef.current?.play();
    }
  };

  const handleAnimationFinish = () => {
    if (isAnimating && onPress) {
      setIsAnimating(false);
      onPress();
    }
  };

  const handleSelectItem = (item: {label: string; value: string}) => {
    setShowDropdown(false);
    if (onSelectItem) {
      onSelectItem(item);
    }
  };

  // Get the appropriate animation based on style
  const buttonAnimation = getButtonAnimation(style);

  // Extract width and height from style for use in components
  const flatStyle = style ? StyleSheet.flatten(style) : {};
  const {width: styleWidth} = flatStyle;

  return (
    <View style={{position: 'relative'}}>
      <TouchableHighlight
        style={[
          styles.button,
          highlighted ? styles.highlightedButton : {},
          style,
        ]}
        onPress={handlePress}
        activeOpacity={1}
        underlayColor="#DFFC4F">
        <View
          style={[
            styles.buttonContainer,
            {
              alignItems: icon ? 'flex-start' : 'center',
            },
          ]}>
          <View
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
            }}>
            <LottieView
              ref={animationRef}
              source={buttonAnimation}
              loop={false}
              style={{
                width: '100%',
                height: '100%',
                opacity: isAnimating ? 1 : 0,
              }}
              onAnimationFinish={handleAnimationFinish}
            />
          </View>
          <View
            style={[
              styles.buttonContent,
              dropdown ? styles.dropdownButtonContent : {},
              {
                paddingLeft: icon ? scale(20) : 0,
              },
              buttonContentContainerStyle,
            ]}>
            {icon && (
              <View style={styles.imageContainer}>
                <Icon
                  name={icon}
                  color={KioskConfig.theme.colors.text.primary}
                  size={size || scale(50)}
                />
              </View>
            )}
            {brand ? (
              <View style={{width: '100%', alignItems: 'center'}}>
                <Icon
                  name={getBrandName(brand)}
                  size={
                    brand === 'head'
                      ? scale(30)
                      : brand === 'bullaPadel'
                        ? scale(70)
                        : brand === 'nox'
                          ? scale(40)
                          : brand === 'technifibre'
                            ? scale(60)
                            : scale(50)
                  }
                  color="black"
                />
              </View>
            ) : (
              <View
                style={{
                  flexDirection: 'column',
                }}>
                <Typography
                  variant="customButton"
                  style={
                    textStyle
                      ? [styles.buttonText, textStyle as TextStyle]
                      : styles.buttonText
                  }>
                  {text}
                </Typography>
                {text2 && (
                  <Typography
                    variant="customButton"
                    style={
                      textStyle
                        ? [styles.buttonText, textStyle as TextStyle]
                        : styles.buttonText
                    }>
                    {text2}
                  </Typography>
                )}
              </View>
            )}
            {dropdown && (
              <Icon
                name="dropdown"
                color={KioskConfig.theme.colors.text.primary}
                size={scale(40)}
                style={styles.dropdownIcon}
              />
            )}
          </View>
        </View>
      </TouchableHighlight>

      {dropdown && (
        <Modal
          visible={showDropdown}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDropdown(false)}>
          <TouchableWithoutFeedback onPress={() => setShowDropdown(false)}>
            <View style={styles.modalOverlay}>
              <View
                style={[
                  styles.dropdownContainer,
                  {top: verticalScale(110)},
                  styleWidth !== undefined ? {width: styleWidth} : {},
                ]}>
                <ScrollView>
                  {itemsToShow.map((item, index) => (
                    <TouchableHighlight
                      key={index}
                      style={[styles.button, styles.dropdownItem]}
                      onPress={() => handleSelectItem(item)}
                      activeOpacity={1}
                      underlayColor="#DFFC4F">
                      <Typography
                        variant="customButton"
                        style={
                          textStyle
                            ? [styles.buttonText, textStyle as TextStyle]
                            : styles.buttonText
                        }>
                        {item.label}
                      </Typography>
                    </TouchableHighlight>
                  ))}
                </ScrollView>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
    justifyContent: 'center',
  },
  button: {
    backgroundColor: KioskConfig.theme.colors.white,
    borderRadius: scale(124),
    maxWidth: scale(596),
    // minWidth: scale(596),
    height: verticalScale(104),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: scale(2),
    borderColor: '#DFFC4F',
  },
  highlightedButton: {
    backgroundColor: '#DFFC4F',
  },
  buttonText: {
    color: '#262626',
    fontSize: moderateScale(48),
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: verticalScale(48),
    fontFamily: FONTS.cocogooseLight,
    letterSpacing: moderateScale(-0.96),
  },
  buttonContent: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  dropdownButtonContent: {
    position: 'relative',
    width: '100%',
    paddingHorizontal: scale(20),
  },
  image: {
    width: scale(333),
    height: verticalScale(51.3),
  },
  imageContainer: {
    minWidth: scale(80),
    minHeight: scale(80),
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownIcon: {
    position: 'absolute',
    right: scale(20),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownContainer: {
    position: 'absolute',
    maxHeight: verticalScale(900),
    width: scale(596),
    backgroundColor: 'white',
    borderRadius: scale(20),
    borderWidth: scale(2),
    borderColor: '#DFFC4F',
    overflow: 'hidden',
  },
  dropdownItem: {
    borderRadius: 0,
    borderWidth: 0,
    borderBottomWidth: scale(1),
    borderColor: '#EFEFEF',
  },
});

export default CustomButton;
