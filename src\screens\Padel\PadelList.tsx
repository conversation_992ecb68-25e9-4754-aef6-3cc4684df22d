import React, {useRef, useState} from 'react';
import GradientBackground from '../../components/GradientBackground';
import {
  Dimensions,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import BrandedPadelCategory from '@/components/BrandedPadelCategory';
import {PadelData} from '@/utils/staticData';
import {scale, verticalScale} from '@/utils/scale';
import {getBrandName} from '@/utils/CommonFunctions';
import Icon from '@/components/Icon';

type PadelListScreenRouteProp = RouteProp<MainStackParamList, 'PadelList'>;

const PadelListScreen = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const route = useRoute<PadelListScreenRouteProp>();
  const {brand} = route.params || {};
  const screenWidth = Dimensions.get('window').width * 0.95;
  const ITEM_WIDTH = screenWidth / 3;
  const flatListRef = useRef<FlatList>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const contentWidth = event.nativeEvent.contentSize.width;
    const layoutWidth = event.nativeEvent.layoutMeasurement.width;

    setShowLeftArrow(contentOffsetX > 0);
    setShowRightArrow(contentOffsetX < contentWidth - layoutWidth - 1);

    // Update current index based on scroll position
    const newIndex = Math.round(contentOffsetX / ITEM_WIDTH);
    setCurrentIndex(newIndex);
  };

  const scrollToNext = () => {
    if (flatListRef.current) {
      const nextIndex = Math.min(currentIndex + 3, PadelData.length - 1);
      flatListRef.current.scrollToIndex({
        index: nextIndex,
        animated: true,
        viewPosition: 0,
      });
    }
  };

  const scrollToPrevious = () => {
    if (flatListRef.current) {
      const prevIndex = Math.max(currentIndex - 3, 0);
      flatListRef.current.scrollToIndex({
        index: prevIndex,
        animated: true,
        viewPosition: 0,
      });
    }
  };
  return (
    <GradientBackground>
      <View style={styles.brandContainer}>
        <Icon
          name={getBrandName(brand)}
          size={brand === 'nox' ? scale(50) : scale(80)}
          color="white"
        />
      </View>
      <View style={styles.container}>
        {showLeftArrow && (
          <TouchableOpacity
            style={[styles.arrowButton, styles.leftArrow]}
            onPress={scrollToPrevious}>
            <Icon name="Left-chevron" size={50} color="#fff" />
          </TouchableOpacity>
        )}
        <FlatList
          ref={flatListRef}
          data={PadelData}
          horizontal
          keyExtractor={item => String(item.id)}
          showsHorizontalScrollIndicator={false}
          snapToInterval={ITEM_WIDTH}
          decelerationRate="fast"
          onScroll={handleScroll}
          scrollEventThrottle={16}
          getItemLayout={(data, index) => ({
            length: ITEM_WIDTH,
            offset: ITEM_WIDTH * index,
            index,
          })}
          renderItem={({item}) => (
            <TouchableOpacity
              activeOpacity={1}
              style={{width: ITEM_WIDTH, alignItems: 'center'}}>
              <BrandedPadelCategory
                key={item.id}
                data={item}
                addOnPress={() => {
                  navigation.navigate('PadelDetails', {
                    padelData: item,
                  });
                }}
              />
            </TouchableOpacity>
          )}
        />
        {showRightArrow && (
          <TouchableOpacity
            style={[styles.arrowButton, styles.rightArrow]}
            onPress={scrollToNext}>
            <Icon name="Right-chevron" size={50} color="#fff" />
          </TouchableOpacity>
        )}
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: verticalScale(20),
    justifyContent: 'center',
  },
  arrowButton: {
    position: 'absolute',
    top: '50%',
    transform: [{translateY: -20}],
    borderRadius: 25,
    padding: 10,
    zIndex: 1,
  },
  leftArrow: {
    left: 10,
  },
  rightArrow: {
    right: 10,
  },

  brandContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: verticalScale(20),
  },
});
export default PadelListScreen;
