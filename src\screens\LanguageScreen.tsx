import React from 'react';
import GradientBackground from '../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import CustomButton from '../components/CustomButton';
import Typography from '../components/Typography';
import KioskConfig from '../config/KioskConfig';
import {useLanguageStore, SupportedLanguage} from '../store/languageStore';
import {useNavigation} from '@react-navigation/native';

const LanguageScreen = () => {
  const {currentLanguage, setLanguage, t} = useLanguageStore();
  const navigation = useNavigation();

  const handleLanguageChange = (language: SupportedLanguage) => {
    setLanguage(language);
    // Navigate back after language change
    navigation.goBack();
  };

  return (
    <GradientBackground>
      <View style={styles.container}>
        <View style={styles.titleContainer}>
          <Typography variant="title" color={KioskConfig.theme.colors.white}>
            {t('language.title')}
          </Typography>
        </View>
        <View style={styles.buttonContainer}>
          <CustomButton
            text={t('language.english')}
            style={styles.button}
            onPress={() => handleLanguageChange('en')}
            highlighted={currentLanguage === 'en'}
          />
          <CustomButton
            text={t('language.spanish')}
            style={styles.button}
            onPress={() => handleLanguageChange('es')}
            highlighted={currentLanguage === 'es'}
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    marginBottom: 50,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 55,
  },
  button: {
    width: 520,
    height: 172,
  },
});
export default LanguageScreen;
