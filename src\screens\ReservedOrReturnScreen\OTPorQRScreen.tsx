import React, {useState} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import GradientBackground from '../../components/GradientBackground';
import KioskConfig from '../../config/KioskConfig';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '../../navigation';
import {verticalScale} from '@/utils/scale';
import QRScanner from '@/components/QRScanner';
import {User} from '@/store/authStore';

const OTPorQRScreen = () => {
  const [error, setError] = useState<string | null>(null);
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();

  // Reset error when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      setError(null);
    }, []),
  );

  // Handle login success
  const handleLoginSuccess = (user?: User) => {
    // Navigate to greeting screen
    navigation.navigate('GreetingScreen', {userData: user});
  };

  // Handle login error
  const handleLoginError = (errorMessage: string) => {
    setError(errorMessage);
  };

  return (
    <GradientBackground>
      <View style={styles.container}>
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        <QRScanner
          key="qr-scanner-direct"
          directLogin={true}
          onLoginSuccess={handleLoginSuccess}
          onLoginError={handleLoginError}
          isActive={true}
        />

        <Text style={styles.instructionText}>Scan QR Code to login</Text>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: verticalScale(100),
  },
  errorContainer: {
    padding: 15,
    backgroundColor: 'rgba(255, 0, 0, 0.2)',
    borderRadius: 8,
    marginBottom: 20,
    width: '80%',
    zIndex: 10,
  },
  errorText: {
    color: KioskConfig.theme.colors.white,
    textAlign: 'center',
    fontSize: 16,
  },
  instructionText: {
    color: KioskConfig.theme.colors.white,
    fontSize: 18,
    marginTop: 20,
    textAlign: 'center',
  },
});

export default OTPorQRScreen;
